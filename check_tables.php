<?php
require_once 'config/database.php';

try {
    echo "Checking database tables...\n";
    
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "No tables found. Running setup...\n";
        require_once 'database_setup.php';
        setupDatabase();
        echo "Setup completed!\n";
        
        // Check again
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    }
    
    echo "Tables in database:\n";
    foreach ($tables as $table) {
        echo "- $table\n";
    }
    
    // Check sectors data
    $sectors = $pdo->query("SELECT COUNT(*) FROM sectors")->fetchColumn();
    echo "\nSectors count: $sectors\n";
    
    // Check governorates data
    $governorates = $pdo->query("SELECT COUNT(*) FROM governorates")->fetchColumn();
    echo "Governorates count: $governorates\n";
    
    echo "\nDatabase is ready!\n";
    
} catch(Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
