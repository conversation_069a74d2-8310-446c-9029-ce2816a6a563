<?php
session_start();
require_once 'config/database.php';
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام تسجيل حملات الحصر الضريبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <img src="logo-nowords.png" alt="Logo" height="40" class="me-2">
                نظام تسجيل حملات الحصر الضريبي
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.php">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="pages/add_campaign.php">
                            <i class="fas fa-plus"></i> إضافة حملة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="pages/campaigns_list.php">
                            <i class="fas fa-list"></i> قائمة الحملات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="pages/reports.php">
                            <i class="fas fa-chart-bar"></i> التقارير
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog"></i> الإعدادات
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="pages/manage_governorates.php">إدارة المحافظات</a></li>
                            <li><a class="dropdown-item" href="pages/manage_sectors.php">إدارة القطاعات</a></li>
                            <li><a class="dropdown-item" href="pages/manage_tax_offices.php">إدارة المأموريات</a></li>
                            <li><a class="dropdown-item" href="pages/import_csv.php">استيراد CSV</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <!-- Dashboard Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title">إجمالي الحملات</h4>
                                <h2 id="total-campaigns">0</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clipboard-list fa-3x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title">الأنشطة المسجلة</h4>
                                <h2 id="registered-activities">0</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check-circle fa-3x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title">الأنشطة غير المسجلة</h4>
                                <h2 id="unregistered-activities">0</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-exclamation-triangle fa-3x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="card-title">المحافظات المشمولة</h4>
                                <h2 id="covered-governorates">0</h2>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-map-marker-alt fa-3x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bolt"></i> الإجراءات السريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <a href="pages/add_campaign.php" class="btn btn-primary btn-lg w-100">
                                    <i class="fas fa-plus-circle"></i>
                                    <br>إضافة حملة جديدة
                                </a>
                            </div>
                            <div class="col-md-4 mb-3">
                                <a href="pages/reports.php" class="btn btn-success btn-lg w-100">
                                    <i class="fas fa-chart-line"></i>
                                    <br>عرض التقارير
                                </a>
                            </div>
                            <div class="col-md-4 mb-3">
                                <a href="pages/import_csv.php" class="btn btn-info btn-lg w-100">
                                    <i class="fas fa-file-import"></i>
                                    <br>استيراد بيانات CSV
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Campaigns -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-clock"></i> آخر الحملات المضافة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="recent-campaigns-table">
                                <thead>
                                    <tr>
                                        <th>الرقم المرجعي</th>
                                        <th>تاريخ الحملة</th>
                                        <th>المحافظة</th>
                                        <th>القطاع</th>
                                        <th>الاسم التجاري</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Data will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p>&copy; 2025 نظام تسجيل حملات الحصر الضريبي - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="assets/js/main.js"></script>

    <script>
        // Load dashboard data
        $(document).ready(function() {
            loadDashboardData();
            loadRecentCampaigns();
        });

        function loadDashboardData() {
            $.ajax({
                url: 'api/dashboard_data.php',
                method: 'GET',
                dataType: 'json',
                success: function(data) {
                    $('#total-campaigns').text(data.total_campaigns || 0);
                    $('#registered-activities').text(data.registered_activities || 0);
                    $('#unregistered-activities').text(data.unregistered_activities || 0);
                    $('#covered-governorates').text(data.covered_governorates || 0);
                },
                error: function() {
                    console.log('Error loading dashboard data');
                }
            });
        }

        function loadRecentCampaigns() {
            $.ajax({
                url: 'api/recent_campaigns.php',
                method: 'GET',
                dataType: 'json',
                success: function(data) {
                    let tbody = $('#recent-campaigns-table tbody');
                    tbody.empty();

                    if (data.length > 0) {
                        data.forEach(function(campaign) {
                            let statusBadge = campaign.is_registered == 1 ?
                                '<span class="badge bg-success">مسجل</span>' :
                                '<span class="badge bg-warning">غير مسجل</span>';

                            tbody.append(`
                                <tr>
                                    <td>${campaign.reference_number}</td>
                                    <td>${campaign.campaign_date}</td>
                                    <td>${campaign.governorate_name}</td>
                                    <td>${campaign.sector_name}</td>
                                    <td>${campaign.business_name}</td>
                                    <td>${statusBadge}</td>
                                    <td>
                                        <a href="pages/view_campaign.php?id=${campaign.id}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="pages/edit_campaign.php?id=${campaign.id}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                            `);
                        });
                    } else {
                        tbody.append('<tr><td colspan="7" class="text-center">لا توجد حملات مسجلة</td></tr>');
                    }
                },
                error: function() {
                    console.log('Error loading recent campaigns');
                }
            });
        }
    </script>
</body>
</html>