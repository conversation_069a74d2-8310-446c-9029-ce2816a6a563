﻿<html xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:mshelp="http://msdn.microsoft.com/mshelp" xmlns:mshelp="http://msdn.microsoft.com/mshelp" xmlns:ddue="http://ddue.schemas.microsoft.com/authoring/2003/5" xmlns:msxsl="urn:schemas-microsoft-com:xslt">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="save" content="history" />
    <title>Readme</title>
    <style type="text/css">
        body {
            background: #FFFFFF;
            color: #000000;
            font-family: Verdana;
            font-size: medium;
            font-style: normal;
            font-weight: normal;
            margin-top: 0;
            margin-bottom: 0;
            margin-left: 0;
            margin-right: 0;
            width: 100%;
        }

        div.#mainSection {
            font-size: 70%;
            width: 100%;
            padding-left: 10;
            margin-right: 10;
        }

        div.#mainBody {
            font-size: 90%;
            margin-top: 10;
            padding-bottom: 20;
        }

        div.#header {
            background-color: #D2D2D2;
            padding-top: 0;
            padding-bottom: 0;
            padding-left: 10;
            padding-right: 0;
            width: 100%;
        }

            div.#header table {
                border-bottom-color: #C8CDDE;
                border-bottom-style: solid;
                border-bottom-width: 1;
                width: 100%;
            }

        span.#runningHeaderText {
            color: #003399;
            font-size: 90%;
        }

        span.#nsrTitle {
            /*    color: #003399;*/
            font-size: 120%;
            font-weight: 600;
        }

        div.#header table td {
            color: #000000;
            font-size: 70%;
            margin-top: 0;
            margin-bottom: 0;
            padding-right: 20;
        }

        div.#header table tr.#headerTableRow3 td {
            padding-bottom: 2;
            padding-top: 5;
        }

        div.#header table.#bottomTable {
            border-top-color: #FFFFFF;
            border-top-style: solid;
            border-top-width: 1;
            text-align: left;
        }

        div.#footer {
            font-size: 90%;
            margin-top: 0;
            margin-bottom: 0;
            margin-left: -5;
            margin-right: 0;
            padding-top: 2;
            padding-bottom: 2;
            padding-left: 0;
            padding-right: 0;
            width: 100%;
        }

        hr.#footerHR {
            border-bottom-color: #EEEEFF;
            border-bottom-style: solid;
            border-bottom-width: 1;
            border-top-color: C8CDDE;
            border-top-style: solid;
            border-top-width: 1;
            height: 3;
            color: #D2D2D2;
        }

        div.section {
            padding-top: 2;
            padding-bottom: 2;
            padding-right: 15;
            width: 100%;
        }

        .heading {
            color: #000000;
            font-weight: bold;
            margin-top: 18;
            margin-bottom: 8;
        }

        h1.heading {
            color: #000000;
            font-size: 150%;
        }

        .subHeading {
            color: #000000;
            font-weight: bold;
            font-size: 150%;
            margin-bottom: 4;
        }

        h2.subHeading {
            color: #000000;
            font-weight: bold;
            font-size: 130%;
        }

        h3.subHeading {
            color: #000000;
            font-size: 125%;
            font-weight: bold;
        }

        h4.subHeading {
            color: #000000;
            font-size: 110%;
            font-weight: bold;
        }

        h4.procedureHeading {
            color: #000080;
            font-size: 110%;
            font-weight: bold;
        }

        h5.subHeading {
            color: #000000;
            font-size: 100%;
            font-weight: bold;
        }

        img {
            padding-bottom: 10;
        }

            img.toggle {
                border: 0;
                margin-right: 5;
                padding-bottom: 10;
            }

            img.copyCodeImage {
                border: 0;
                margin: 1;
                margin-right: 3;
                padding-bottom: 10;
            }

            img.downloadCodeImage {
                border: 0;
                margin-right: 3;
                padding-bottom: 10;
            }

            img.viewCodeImage {
                border: 0;
                margin-right: 3;
                padding-bottom: 10;
            }

            img.note {
                border: 0;
                margin-right: 3;
                padding-bottom: 10;
            }

            img.#membersOptionsFilterImage {
                border: 0;
                margin-left: 10;
                vertical-align: middle;
                padding-bottom: 10;
            }

            img.#toggleAllImage {
                margin-left: 4;
                vertical-align: middle;
                padding-bottom: 10;
            }

        div.#mainSection table {
            border: 0;
            font-size: 100%;
            width: 100%;
            margin-top: 5px;
            margin-bottom: 15px;
        }

            div.#mainSection table tr {
                vertical-align: top;
            }

            div.#mainSection table th {
                text-align: left;
                background: #D8D8D8;
                border-bottom-color: #D8D8D8;
                border-bottom-style: solid;
                border-bottom-width: 1;
                color: #000000;
                padding-left: 5;
                padding-right: 5;
            }

            div.#mainSection table td {
                background: #F2F2F2;
                border-top-color: #D8D8D8;
                border-top-style: solid;
                border-top-width: 1;
                padding-left: 5;
                padding-right: 5;
            }

                div.#mainSection table td.imageCell {
                    white-space: nowrap;
                }

        div.code {
            width: 98%;
        }

            div.code table {
                border: 0;
                font-size: 95%;
                margin-bottom: 5;
                width: 100%;
            }

                div.code table th {
                    text-align: left;
                    background: #D8D8D8;
                    border-bottom-color: #D8D8D8;
                    border-bottom-style: solid;
                    border-bottom-width: 1;
                    color: #000000;
                    font-weight: bold;
                    padding-left: 5;
                    padding-right: 5;
                }

                div.code table td {
                    background: #CCCCCC;
                    border-top-color: #D8D8D8;
                    border-top-style: solid;
                    border-top-width: 1;
                    padding-left: 5;
                    padding-right: 5;
                    padding-top: 5;
                }

        div.alert {
            margin-left: 10;
            width: 98%;
        }

            div.alert table {
                border: 1;
                font-size: 100%;
                width: 100%;
                border: solid 1 #DEDFEF;
            }

                div.alert table th {
                    text-align: left;
                    background: #D8D8D8;
                    border-bottom-width: 0;
                    color: #000000;
                    padding-left: 5;
                    padding-right: 5;
                    border: solid 1 #DEDFEF;
                }

                div.alert table td {
                    background: #FFFFFF;
                    border-top-color: #D8D8D8;
                    border-top-style: solid;
                    border-top-width: 1;
                    padding-left: 5;
                    padding-right: 5;
                    border: solid 1 #DEDFEF;
                }

        span.copyCode {
            color: #0000ff;
            font-size: 90%;
            font-weight: normal;
            cursor: hand;
            float: right;
            display: inline;
            text-align: right;
        }

        .downloadCode {
            color: #0000ff;
            font-size: 90%;
            font-weight: normal;
            cursor: hand;
        }

        .viewCode {
            color: #0000ff;
            font-size: 90%;
            font-weight: normal;
            cursor: hand;
        }

        div.code pre {
            font-family: Monospace, Courier New, Courier;
            font-size: 105%;
            color: #000000;
        }

        code {
            font-family: Monospace, Courier New, Courier;
            font-size: 105%;
            color: #000000;
        }

        dl {
            margin-top: 0;
            padding-left: 1;
        }

        dd {
            margin-bottom: 0;
            margin-left: 0;
            padding-left: 20;
        }

            dd p {
                margin-top: 5;
            }

        ul {
            margin-left: 17;
            list-style-type: disc;
        }

            ul ul {
                margin-bottom: 4;
                margin-left: 17;
                margin-top: 3;
                list-style-type: disc;
            }

        ol {
            margin-left: 24;
            list-style-type: decimal;
        }

            ol ol {
                margin-left: 24;
                margin-top: 3;
                list-style-type: lower-alpha;
            }

        li {
            margin-top: 0;
            margin-bottom: 0;
            padding-bottom: 0;
            padding-top: 0;
            margin-left: 5;
        }

            li p {
                margin-top: 0;
                margin-bottom: 0;
            }

        p {
            margin-bottom: 15;
        }

        .tip {
            color: #0000FF;
            font-style: italic;
            cursor: hand;
            text-decoration: underline;
        }

        .math {
            font-family: Times New Roman;
            font-size: 125%;
        }

        .sourceCodeList {
            font-family: Verdana;
            font-size: 90%;
        }

        pre.viewCode {
            width: 100%;
            overflow: auto;
        }

        li:hover table, li.over table {
            background-color: #C0C0C0;
        }

        li:hover ul, li.over ul {
            background-color: #d2d2d2;
            border: 1px solid #000;
            display: block;
        }
    </style>
</head>
<body>
    <div id="header">
        <table id="bottomTable" cellpadding="0" cellspacing="0">
            <tr>
                <td align="left">
                    <span id="nsrTitle">Readme</span>
                </td>
            </tr>
        </table>
    </div>
    <div id="mainSection">
        <div id="mainBody">
            <p>Jan 2024</p>
            <div class="introduction"><p>Use this readme file to help you configure the Microsoft Drivers for PHP for SQL Server.</p></div>
            <h1 class="heading">Installation</h1><div id="sectionSection0" class="section" name="collapseableSection" style="">
                <p>This section lists the drivers that are installed for the Microsoft Drivers 5.12 for PHP for SQL Server. PHP versions prior to 8.0 are not supported in this release.</p><div class="caption" /><div class="tableSection">
                    <table width="50%" cellspacing="2" cellpadding="5" frame="lhs">
                        <tr><th><p>Driver file</p></th><th><p>PHP version</p></th><th><p>Thread safe?</p></th><th><p>Use with PHP .dll</p></th></tr>
                        <tr><td><p>php_sqlsrv_83_nts_x86.dll</p><p>php_pdo_sqlsrv_83_nts_x86.dll</p></td><td><p>8.3</p></td><td><p>no</p></td><td><p>php8.dll</p></td></tr>
                        <tr><td><p>php_sqlsrv_83_ts_x86.dll</p><p>php_pdo_sqlsrv_83_ts_x86.dll</p></td><td><p>8.3</p></td><td><p>yes</p></td><td><p>php8ts.dll</p></td></tr>
                        <tr><td><p>php_sqlsrv_83_nts_x64.dll</p><p>php_pdo_sqlsrv_83_nts_x64.dll</p></td><td><p>8.3</p></td><td><p>no</p></td><td><p>php8.dll</p></td></tr>
                        <tr><td><p>php_sqlsrv_83_ts_x64.dll</p><p>php_pdo_sqlsrv_83_ts_x64.dll</p></td><td><p>8.3</p></td><td><p>yes</p></td><td><p>php8ts.dll</p></td></tr>
                        <tr><td><p>php_sqlsrv_82_nts_x86.dll</p><p>php_pdo_sqlsrv_82_nts_x86.dll</p></td><td><p>8.2</p></td><td><p>no</p></td><td><p>php8.dll</p></td></tr>
                        <tr><td><p>php_sqlsrv_82_ts_x86.dll</p><p>php_pdo_sqlsrv_82_ts_x86.dll</p></td><td><p>8.2</p></td><td><p>yes</p></td><td><p>php8ts.dll</p></td></tr>
                        <tr><td><p>php_sqlsrv_82_nts_x64.dll</p><p>php_pdo_sqlsrv_82_nts_x64.dll</p></td><td><p>8.2</p></td><td><p>no</p></td><td><p>php8.dll</p></td></tr>
                        <tr><td><p>php_sqlsrv_82_ts_x64.dll</p><p>php_pdo_sqlsrv_82_ts_x64.dll</p></td><td><p>8.2</p></td><td><p>yes</p></td><td><p>php8ts.dll</p></td></tr>
                        <tr><td><p>php_sqlsrv_81_nts_x86.dll</p><p>php_pdo_sqlsrv_81_nts_x86.dll</p></td><td><p>8.1</p></td><td><p>no</p></td><td><p>php8.dll</p></td></tr>
                        <tr><td><p>php_sqlsrv_81_ts_x86.dll</p><p>php_pdo_sqlsrv_81_ts_x86.dll</p></td><td><p>8.1</p></td><td><p>yes</p></td><td><p>php8ts.dll</p></td></tr>
                        <tr><td><p>php_sqlsrv_81_nts_x64.dll</p><p>php_pdo_sqlsrv_81_nts_x64.dll</p></td><td><p>8.1</p></td><td><p>no</p></td><td><p>php8.dll</p></td></tr>
                        <tr><td><p>php_sqlsrv_81_ts_x64.dll</p><p>php_pdo_sqlsrv_81_ts_x64.dll</p></td><td><p>8.1</p></td><td><p>yes</p></td><td><p>php8ts.dll</p></td></tr>
                    </table>
                </div>
            </div>
            <h1 class="heading">Support</h1><div id="sectionSection1" class="section" name="collapseableSection" style="">
                <p>Documentation for this release is available on <a href="https://docs.microsoft.com/sql/connect/php/microsoft-php-driver-for-sql-server">Microsoft Docs</a>.</p><p>The documentation also contains support information (see the Support Resources topic).</p><p>For information about supported operating systems, see System Requirements in the documentation.</p><div class="alert"><table width="100%" cellspacing="0" cellpadding="0"><tr><th align="left"><b>Note</b></th></tr><tr><td><p>Only versions of the driver released by Microsoft are supported by Microsoft Customer Service and Support.</p></td></tr></table></div>
            </div>
            <h1 class="heading">Source Code Availability</h1><div id="sectionSection2" class="section" name="collapseableSection" style=""><p>The source code for the Microsoft Drivers for PHP for SQL Server is available <a href="https://github.com/microsoft/msphpsql">here</a>.</p></div>
        </div><div id="footer" />
    </div>
</body>
</html>