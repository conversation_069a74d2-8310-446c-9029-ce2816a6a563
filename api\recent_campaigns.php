<?php
/**
 * Recent Campaigns API
 * Returns the most recent campaigns for the dashboard
 */

header('Content-Type: application/json');
require_once '../config/database.php';

try {
    $sql = "
        SELECT TOP 10
            c.id,
            c.reference_number,
            c.campaign_date,
            c.business_name,
            c.is_registered,
            g.name as governorate_name,
            s.name as sector_name
        FROM campaigns c
        LEFT JOIN governorates g ON c.governorate_id = g.id
        LEFT JOIN sectors s ON c.sector_id = s.id
        ORDER BY c.created_at DESC
    ";

    $campaigns = getRecords($sql);

    // Format the data
    foreach ($campaigns as &$campaign) {
        $campaign['campaign_date'] = formatDate($campaign['campaign_date'], 'd/m/Y');
        $campaign['is_registered'] = (bool)$campaign['is_registered'];
    }

    echo json_encode($campaigns, JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => $e->getMessage()
    ];

    echo json_encode($response, JSON_UNESCAPED_UNICODE);
}
?>