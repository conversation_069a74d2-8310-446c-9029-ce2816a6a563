<?php
require_once 'config/database.php';

try {
    echo "Inserting initial data...\n";
    
    // Insert sectors
    $sectors = [
        ['name' => 'القاهرة الكبرى', 'code' => 'CGC'],
        ['name' => 'غرب الدلتا والإسكندرية ومطروح', 'code' => 'WDA'],
        ['name' => 'شرق الدلتا ومدن القناة وسيناء', 'code' => 'EDC'],
        ['name' => 'مصر العليا والبحر الأحمر والوادي الجديد', 'code' => 'UER']
    ];
    
    foreach ($sectors as $sector) {
        $sql = "INSERT IGNORE INTO sectors (name, code) VALUES (?, ?)";
        $pdo->prepare($sql)->execute([$sector['name'], $sector['code']]);
    }
    echo "✓ Sectors inserted\n";
    
    // Insert governorates
    $governorates = [
        // القاهرة الكبرى
        ['name' => 'القاهرة', 'code' => 'CAI', 'sector_id' => 1],
        ['name' => 'الجيزة', 'code' => 'GIZ', 'sector_id' => 1],
        ['name' => 'القليوبية', 'code' => 'QAL', 'sector_id' => 1],
        
        // غرب الدلتا والإسكندرية ومطروح
        ['name' => 'الإسكندرية', 'code' => 'ALX', 'sector_id' => 2],
        ['name' => 'البحيرة', 'code' => 'BEH', 'sector_id' => 2],
        ['name' => 'مطروح', 'code' => 'MAT', 'sector_id' => 2],
        ['name' => 'كفر الشيخ', 'code' => 'KFS', 'sector_id' => 2],
        ['name' => 'الغربية', 'code' => 'GHR', 'sector_id' => 2],
        ['name' => 'المنوفية', 'code' => 'MNF', 'sector_id' => 2],
        
        // شرق الدلتا ومدن القناة وسيناء
        ['name' => 'الدقهلية', 'code' => 'DKH', 'sector_id' => 3],
        ['name' => 'الشرقية', 'code' => 'SHR', 'sector_id' => 3],
        ['name' => 'دمياط', 'code' => 'DMT', 'sector_id' => 3],
        ['name' => 'بورسعيد', 'code' => 'PTS', 'sector_id' => 3],
        ['name' => 'الإسماعيلية', 'code' => 'ISM', 'sector_id' => 3],
        ['name' => 'السويس', 'code' => 'SUZ', 'sector_id' => 3],
        ['name' => 'شمال سيناء', 'code' => 'SIN', 'sector_id' => 3],
        ['name' => 'جنوب سيناء', 'code' => 'JSI', 'sector_id' => 3],
        
        // مصر العليا والبحر الأحمر والوادي الجديد
        ['name' => 'الفيوم', 'code' => 'FYM', 'sector_id' => 4],
        ['name' => 'بني سويف', 'code' => 'BNS', 'sector_id' => 4],
        ['name' => 'المنيا', 'code' => 'MNY', 'sector_id' => 4],
        ['name' => 'أسيوط', 'code' => 'AST', 'sector_id' => 4],
        ['name' => 'سوهاج', 'code' => 'SOH', 'sector_id' => 4],
        ['name' => 'قنا', 'code' => 'QNA', 'sector_id' => 4],
        ['name' => 'الأقصر', 'code' => 'LXR', 'sector_id' => 4],
        ['name' => 'أسوان', 'code' => 'ASW', 'sector_id' => 4],
        ['name' => 'البحر الأحمر', 'code' => 'BAH', 'sector_id' => 4],
        ['name' => 'الوادي الجديد', 'code' => 'WAD', 'sector_id' => 4]
    ];
    
    foreach ($governorates as $gov) {
        $sql = "INSERT IGNORE INTO governorates (name, code, sector_id) VALUES (?, ?, ?)";
        $pdo->prepare($sql)->execute([$gov['name'], $gov['code'], $gov['sector_id']]);
    }
    echo "✓ Governorates inserted\n";
    
    // Check counts
    $sectors_count = $pdo->query("SELECT COUNT(*) FROM sectors")->fetchColumn();
    $governorates_count = $pdo->query("SELECT COUNT(*) FROM governorates")->fetchColumn();
    
    echo "\nData inserted successfully!\n";
    echo "Sectors: $sectors_count\n";
    echo "Governorates: $governorates_count\n";
    
} catch(Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
