<?php
session_start();
require_once '../config/database.php';

// Pagination settings
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

// Search and filter parameters
$search = $_GET['search'] ?? '';
$sector_filter = $_GET['sector'] ?? '';
$governorate_filter = $_GET['governorate'] ?? '';
$registration_filter = $_GET['registration'] ?? '';

// Build WHERE clause
$where_conditions = [];
$params = [];

if ($search) {
    $where_conditions[] = "(c.reference_number LIKE ? OR c.business_name LIKE ? OR c.owner_name LIKE ?)";
    $search_param = "%$search%";
    $params[] = $search_param;
    $params[] = $search_param;
    $params[] = $search_param;
}

if ($sector_filter) {
    $where_conditions[] = "c.sector_id = ?";
    $params[] = $sector_filter;
}

if ($governorate_filter) {
    $where_conditions[] = "c.governorate_id = ?";
    $params[] = $governorate_filter;
}

if ($registration_filter !== '') {
    $where_conditions[] = "c.is_registered = ?";
    $params[] = $registration_filter;
}

$where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total count for pagination
$count_sql = "SELECT COUNT(*) as total FROM campaigns c $where_clause";
$total_records = getRecord($count_sql, $params)['total'];
$total_pages = ceil($total_records / $per_page);

// Get campaigns data
$sql = "
    SELECT
        c.id,
        c.reference_number,
        c.campaign_date,
        c.business_name,
        c.owner_name,
        c.is_registered,
        c.daily_revenue,
        c.created_at,
        s.name as sector_name,
        g.name as governorate_name
    FROM campaigns c
    LEFT JOIN sectors s ON c.sector_id = s.id
    LEFT JOIN governorates g ON c.governorate_id = g.id
    $where_clause
    ORDER BY c.created_at DESC
    OFFSET $offset ROWS FETCH NEXT $per_page ROWS ONLY
";

$campaigns = getRecords($sql, $params);

// Get sectors and governorates for filters
$sectors = getRecords("SELECT id, name FROM sectors ORDER BY name");
$governorates = getRecords("SELECT id, name FROM governorates ORDER BY name");
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قائمة الحملات - نظام تسجيل حملات الحصر الضريبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <img src="../logo-nowords.png" alt="Logo" height="40" class="me-2">
                نظام تسجيل حملات الحصر الضريبي
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="add_campaign.php">
                            <i class="fas fa-plus"></i> إضافة حملة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="campaigns_list.php">
                            <i class="fas fa-list"></i> قائمة الحملات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-chart-bar"></i> التقارير
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Search and Filters -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-search"></i> البحث والفلاتر
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">البحث</label>
                        <input type="text" class="form-control" id="search" name="search"
                               value="<?= htmlspecialchars($search) ?>"
                               placeholder="رقم مرجعي، اسم تجاري، أو صاحب النشاط">
                    </div>
                    <div class="col-md-3">
                        <label for="sector" class="form-label">القطاع</label>
                        <select class="form-select" id="sector" name="sector">
                            <option value="">جميع القطاعات</option>
                            <?php foreach ($sectors as $sector): ?>
                                <option value="<?= $sector['id'] ?>" <?= $sector_filter == $sector['id'] ? 'selected' : '' ?>>
                                    <?= $sector['name'] ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="governorate" class="form-label">المحافظة</label>
                        <select class="form-select" id="governorate" name="governorate">
                            <option value="">جميع المحافظات</option>
                            <?php foreach ($governorates as $gov): ?>
                                <option value="<?= $gov['id'] ?>" <?= $governorate_filter == $gov['id'] ? 'selected' : '' ?>>
                                    <?= $gov['name'] ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="registration" class="form-label">حالة التسجيل</label>
                        <select class="form-select" id="registration" name="registration">
                            <option value="">الكل</option>
                            <option value="1" <?= $registration_filter === '1' ? 'selected' : '' ?>>مسجل</option>
                            <option value="0" <?= $registration_filter === '0' ? 'selected' : '' ?>>غير مسجل</option>
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> بحث
                        </button>
                        <a href="campaigns_list.php" class="btn btn-secondary">
                            <i class="fas fa-undo"></i> إعادة تعيين
                        </a>
                        <a href="add_campaign.php" class="btn btn-success">
                            <i class="fas fa-plus"></i> إضافة حملة جديدة
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Results Summary -->
        <div class="row mb-3">
            <div class="col-md-6">
                <p class="text-muted">
                    عرض <?= count($campaigns) ?> من أصل <?= number_format($total_records) ?> حملة
                </p>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-outline-success btn-sm" onclick="exportCurrentPage()">
                    <i class="fas fa-download"></i> تصدير الصفحة الحالية
                </button>
            </div>
        </div>

        <!-- Campaigns Table -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i> قائمة الحملات
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($campaigns)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد حملات تطابق معايير البحث</h5>
                        <a href="add_campaign.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة حملة جديدة
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الرقم المرجعي</th>
                                    <th>تاريخ الحملة</th>
                                    <th>الاسم التجاري</th>
                                    <th>صاحب النشاط</th>
                                    <th>القطاع</th>
                                    <th>المحافظة</th>
                                    <th>حالة التسجيل</th>
                                    <th>الإيرادات اليومية</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($campaigns as $campaign): ?>
                                    <tr>
                                        <td>
                                            <strong class="text-primary"><?= $campaign['reference_number'] ?></strong>
                                        </td>
                                        <td><?= formatDate($campaign['campaign_date'], 'd/m/Y') ?></td>
                                        <td><?= $campaign['business_name'] ?: '-' ?></td>
                                        <td><?= $campaign['owner_name'] ?: '-' ?></td>
                                        <td><?= $campaign['sector_name'] ?></td>
                                        <td><?= $campaign['governorate_name'] ?></td>
                                        <td>
                                            <?php if ($campaign['is_registered']): ?>
                                                <span class="badge bg-success">مسجل</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning">غير مسجل</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($campaign['daily_revenue']): ?>
                                                <?= number_format($campaign['daily_revenue'], 2) ?> جنيه
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button type="button" class="btn btn-outline-primary"
                                                        onclick="viewCampaign(<?= $campaign['id'] ?>)"
                                                        title="عرض التفاصيل">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-warning"
                                                        onclick="editCampaign(<?= $campaign['id'] ?>)"
                                                        title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-outline-danger"
                                                        onclick="deleteCampaign(<?= $campaign['id'] ?>)"
                                                        title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <nav aria-label="صفحات النتائج" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page - 1])) ?>">
                                            <i class="fas fa-chevron-right"></i> السابق
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php
                                $start_page = max(1, $page - 2);
                                $end_page = min($total_pages, $page + 2);

                                for ($i = $start_page; $i <= $end_page; $i++):
                                ?>
                                    <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                                        <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $i])) ?>">
                                            <?= $i ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?= http_build_query(array_merge($_GET, ['page' => $page + 1])) ?>">
                                            التالي <i class="fas fa-chevron-left"></i>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Campaign Details Modal -->
    <div class="modal fade" id="campaignModal" tabindex="-1" aria-labelledby="campaignModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="campaignModalLabel">تفاصيل الحملة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="campaignModalBody">
                    <!-- Campaign details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p>&copy; 2025 نظام تسجيل حملات الحصر الضريبي - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../assets/js/main.js"></script>

    <script>
        function viewCampaign(id) {
            // Load campaign details via AJAX
            $.get(`../api/campaign_details.php?id=${id}`)
                .done(function(data) {
                    $('#campaignModalBody').html(data);
                    $('#campaignModal').modal('show');
                })
                .fail(function() {
                    showAlert('خطأ في تحميل بيانات الحملة', 'danger');
                });
        }

        function editCampaign(id) {
            window.location.href = `edit_campaign.php?id=${id}`;
        }

        function deleteCampaign(id) {
            if (confirm('هل أنت متأكد من حذف هذه الحملة؟ لا يمكن التراجع عن هذا الإجراء.')) {
                $.post('../api/delete_campaign.php', { id: id })
                    .done(function(response) {
                        if (response.success) {
                            showAlert('تم حذف الحملة بنجاح', 'success');
                            setTimeout(() => location.reload(), 1500);
                        } else {
                            showAlert(response.error || 'حدث خطأ أثناء حذف الحملة', 'danger');
                        }
                    })
                    .fail(function() {
                        showAlert('حدث خطأ أثناء حذف الحملة', 'danger');
                    });
            }
        }

        function exportCurrentPage() {
            const params = new URLSearchParams(window.location.search);
            params.set('export', 'current_page');
            window.location.href = '../api/export_campaigns.php?' + params.toString();
        }
    </script>
</body>
</html>