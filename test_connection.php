<?php
echo "Testing MySQL connection...\n";

try {
    $dsn = "mysql:host=localhost;charset=utf8mb4";
    $pdo = new PDO($dsn, 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✓ MySQL connection successful!\n";
    
    // Create database
    $pdo->exec("CREATE DATABASE IF NOT EXISTS tax_campaigns_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "✓ Database created/exists!\n";
    
    // Connect to database
    $dsn = "mysql:host=localhost;dbname=tax_campaigns_db;charset=utf8mb4";
    $pdo = new PDO($dsn, 'root', '');
    echo "✓ Connected to database!\n";
    
    echo "All tests passed! You can now run database_setup.php\n";
    
} catch(PDOException $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
}
?>
