# نظام تسجيل حملات الحصر الضريبي

## نظرة عامة
نظام ويب عصري لتسجيل نتائج حملات الحصر بالتعاون مع مباحث التهرب الضريبي، مع إمكانيات متقدمة لاستخراج التقارير والتحليل الإحصائي.

## المتطلبات التقنية
- **الخادم**: Apache/Nginx
- **قاعدة البيانات**: Microsoft SQL Server
- **لغة البرمجة**: PHP 7.4+
- **واجهة المستخدم**: HTML5, CSS3, Bootstrap 5
- **JavaScript**: jQuery, Chart.js

## الميزات الرئيسية

### 1. تسجيل الحملات
- تسجيل شامل لبيانات الحملات
- توليد تلقائي للرقم المرجعي
- التحقق من صحة البيانات المدخلة
- دعم الملفات المرفقة

### 2. إدارة البيانات المساعدة
- إدارة القطاعات والمحافظات
- إدارة مأموريات الضرائب
- إدارة أنواع الأنشطة التجارية

### 3. التقارير والإحصائيات
- تقارير مفصلة حسب المحافظة والقطاع
- رسوم بيانية تفاعلية
- مؤشرات الأداء الرئيسية
- إمكانية تصدير البيانات

### 4. استيراد البيانات
- استيراد ملفات CSV
- التحقق من صحة البيانات المستوردة
- معالجة الأخطاء والتحذيرات

## هيكل المشروع

```
TEID/
├── index.php                 # الصفحة الرئيسية
├── database_setup.php        # إعداد قاعدة البيانات
├── README.md                 # ملف التوثيق
├── logo-nowords.png          # شعار النظام
├── config/
│   └── database.php          # إعدادات قاعدة البيانات
├── assets/
│   ├── css/
│   │   └── style.css         # ملف التصميم الرئيسي
│   └── js/
│       └── main.js           # ملف JavaScript الرئيسي
├── pages/
│   ├── add_campaign.php      # صفحة إضافة حملة
│   ├── campaigns_list.php    # قائمة الحملات
│   ├── reports.php           # صفحة التقارير
│   ├── manage_governorates.php # إدارة المحافظات
│   ├── manage_sectors.php    # إدارة القطاعات
│   ├── manage_tax_offices.php # إدارة المأموريات
│   └── import_csv.php        # استيراد CSV
└── api/
    ├── dashboard_data.php    # بيانات لوحة التحكم
    └── recent_campaigns.php  # الحملات الأخيرة
```

## قاعدة البيانات

### الجداول الرئيسية

#### 1. جدول القطاعات (sectors)
- القاهرة الكبرى
- غرب الدلتا والإسكندرية ومطروح
- شرق الدلتا ومدن القناة وسيناء
- مصر العليا والبحر الأحمر والوادي الجديد

#### 2. جدول المحافظات (governorates)
جميع محافظات جمهورية مصر العربية مصنفة حسب القطاعات

#### 3. جدول الحملات (campaigns)
يحتوي على جميع بيانات الحملات المطلوبة:
- بيانات الحملة الأساسية
- بيانات النشاط وصاحب النشاط
- بيانات الوحدة
- موقف التسجيل الضريبي
- بيانات الفاتورة والإيصال الإلكتروني
- الملاحظات الإضافية والمرفقات

## تركيب النظام

### 1. متطلبات الخادم
```bash
# تأكد من تشغيل Apache و SQL Server
# تأكد من تفعيل PHP extensions:
- php_pdo_sqlsrv
- php_sqlsrv
```

### 2. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE tax_campaigns_db;
```

### 3. تكوين الاتصال
قم بتعديل ملف `config/database.php`:
```php
$db_config = [
    'server' => 'localhost',
    'database' => 'tax_campaigns_db',
    'username' => 'sa',
    'password' => 'your_password',
    'charset' => 'UTF-8'
];
```

### 4. تشغيل إعداد قاعدة البيانات
```bash
# قم بزيارة الرابط التالي في المتصفح
http://localhost/TEID/database_setup.php
```

## استخدام النظام

### 1. الصفحة الرئيسية
- عرض إحصائيات سريعة
- الوصول السريع للوظائف الرئيسية
- عرض آخر الحملات المضافة

### 2. إضافة حملة جديدة
1. انتقل إلى "إضافة حملة"
2. املأ جميع البيانات المطلوبة
3. سيتم توليد الرقم المرجعي تلقائياً
4. احفظ البيانات

### 3. عرض التقارير
- تقارير حسب المحافظة
- تقارير حسب القطاع
- تقارير حسب الفترة الزمنية
- رسوم بيانية تفاعلية

### 4. استيراد البيانات
- دعم ملفات CSV
- التحقق التلقائي من البيانات
- معالجة الأخطاء

## الرقم المرجعي
يتم توليد الرقم المرجعي تلقائياً بالتنسيق التالي:
```
YYYY-MM-DD-SECTOR-GOVERNORATE-CAMPAIGN-SERIAL
مثال: 2025-01-15-1-01-001-02
```

## التحقق من صحة البيانات

### الرقم القومي
- يجب أن يكون 14 رقم بالضبط
- أرقام فقط بدون مسافات أو رموز

### رقم الموبايل
- يجب أن يكون 11 رقم
- يبدأ بـ 010, 011, 012, أو 015

### رقم التسجيل الضريبي
- يجب أن يكون 9 أرقام بالضبط

## الأمان
- تنظيف جميع المدخلات
- استخدام Prepared Statements
- التحقق من صحة البيانات
- تسجيل العمليات

## الدعم الفني
للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## الترخيص
جميع الحقوق محفوظة © 2025 نظام تسجيل حملات الحصر الضريبي