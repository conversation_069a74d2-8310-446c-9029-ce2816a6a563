# نظام تسجيل حملات الحصر الضريبي

## ✅ النظام جاهز للاستخدام!

تم إنشاء نظام ويب متكامل لتسجيل وإدارة حملات الحصر الضريبي بالتعاون مع مباحث التهرب الضريبي، مع إمكانيات متقدمة للتقارير والتحليل الإحصائي.

## 🌐 الوصول للنظام
**الرابط**: http://localhost/TEID/

## المتطلبات التقنية
- **الخادم**: XAMPP (Apache + MySQL + PHP) ✅
- **قاعدة البيانات**: MySQL/MariaDB ✅
- **لغة البرمجة**: PHP 8.2+ ✅
- **واجهة المستخدم**: HTML5, CSS3, Bootstrap 5 ✅
- **JavaScript**: jQuery, Chart.js ✅

## ✅ الميزات المكتملة

### 1. تسجيل الحملات ✅
- نموذج شامل لتسجيل بيانات الحملة
- توليد رقم مرجعي تلقائي (16 رقم)
- التحقق من صحة البيانات المصرية (الرقم القومي، الموبايل، التسجيل الضريبي)

### 2. إدارة البيانات ✅
- إدارة المحافظات والقطاعات (27 محافظة + 4 قطاعات)
- إدارة مأموريات الضرائب
- إدارة أنواع الأنشطة

### 3. التقارير والإحصائيات ✅
- لوحة تحكم تفاعلية
- رسوم بيانية تفاعلية
- مؤشرات الأداء
- التحليل الإحصائي
- تصدير البيانات

### 4. استيراد البيانات ✅
- استيراد ملفات CSV
- التحقق من صحة البيانات المستوردة
- معالجة الأخطاء

## 🏗️ هيكل المشروع

```
TEID/
├── index.php                 # الصفحة الرئيسية ✅
├── database_setup.php        # إعداد قاعدة البيانات ✅
├── config/
│   └── database.php          # إعدادات قاعدة البيانات ✅
├── assets/
│   ├── css/style.css         # التصميم الرئيسي ✅
│   └── js/main.js            # JavaScript الرئيسي ✅
├── pages/
│   ├── add_campaign.php      # إضافة حملة ✅
│   ├── campaigns_list.php    # قائمة الحملات ✅
│   ├── reports.php           # التقارير ✅
│   └── import_csv.php        # استيراد CSV ✅
└── api/
    ├── dashboard_data.php    # بيانات لوحة التحكم ✅
    └── recent_campaigns.php  # الحملات الأخيرة ✅
```

## 🚀 كيفية البدء

### 1. التأكد من تشغيل XAMPP
- تشغيل Apache ✅
- تشغيل MySQL ✅

### 2. الوصول للنظام
- افتح المتصفح واذهب إلى: http://localhost/TEID/
- ستظهر لوحة التحكم الرئيسية

### 3. البيانات الأساسية
- تم إدراج 4 قطاعات رئيسية ✅
- تم إدراج 27 محافظة مصرية ✅
- قاعدة البيانات جاهزة للاستخدام ✅

## 📊 الوظائف الرئيسية

### إضافة حملة جديدة
1. اضغط على "إضافة حملة" من القائمة الرئيسية
2. املأ جميع البيانات المطلوبة
3. سيتم توليد الرقم المرجعي تلقائياً
4. احفظ البيانات

### عرض قائمة الحملات
- عرض جميع الحملات المسجلة
- البحث والفلترة
- تصدير البيانات

### التقارير والإحصائيات
- رسوم بيانية تفاعلية
- إحصائيات مفصلة
- مؤشرات الأداء

### استيراد البيانات
- استيراد ملفات CSV
- التحقق من صحة البيانات
- معالجة الأخطاء

## 🔧 الإعدادات

### قاعدة البيانات
- **الخادم**: localhost
- **قاعدة البيانات**: tax_campaigns_db
- **المستخدم**: root
- **كلمة المرور**: (فارغة)

### الملفات المهمة
- `config/database.php` - إعدادات قاعدة البيانات
- `database_setup.php` - إعداد الجداول والبيانات الأولية
- `insert_initial_data.php` - إدراج البيانات الأساسية

## 🎯 الخطوات التالية

النظام جاهز للاستخدام! يمكنك الآن:
1. إضافة حملات جديدة
2. استيراد بيانات من ملفات CSV
3. عرض التقارير والإحصائيات
4. إدارة البيانات المساعدة

## 📞 الدعم الفني

في حالة وجود أي مشاكل:
1. تأكد من تشغيل XAMPP
2. تأكد من إعدادات قاعدة البيانات
3. راجع ملفات السجل للأخطاء

---
**تم إنجاز المشروع بنجاح! 🎉**
