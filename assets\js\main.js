/**
 * نظام تسجيل حملات الحصر الضريبي
 * Main JavaScript Functions
 */

$(document).ready(function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add fade-in animation to cards
    $('.card').addClass('fade-in-up');

    // Form validation
    initializeFormValidation();

    // Initialize date pickers
    initializeDatePickers();

    // Initialize number formatting
    initializeNumberFormatting();
});

/**
 * Form Validation
 */
function initializeFormValidation() {
    // National ID validation
    $('input[name="national_id"], input[name="owner_national_id"]').on('input', function() {
        validateNationalId($(this));
    });

    // Mobile number validation
    $('input[name="mobile"]').on('input', function() {
        validateMobileNumber($(this));
    });

    // Tax registration number validation
    $('input[name="tax_registration_number"]').on('input', function() {
        validateTaxRegistrationNumber($(this));
    });
}

/**
 * Validate Egyptian National ID
 */
function validateNationalId(input) {
    const value = input.val().replace(/\s/g, '');
    const isValid = /^\d{14}$/.test(value);

    if (value.length > 0) {
        if (isValid) {
            input.removeClass('is-invalid').addClass('is-valid');
            input.siblings('.invalid-feedback').hide();
        } else {
            input.removeClass('is-valid').addClass('is-invalid');
            input.siblings('.invalid-feedback').text('الرقم القومي يجب أن يكون 14 رقم').show();
        }
    } else {
        input.removeClass('is-valid is-invalid');
        input.siblings('.invalid-feedback').hide();
    }

    return isValid;
}

/**
 * Validate Egyptian Mobile Number
 */
function validateMobileNumber(input) {
    const value = input.val().replace(/\s/g, '');
    const isValid = /^(010|011|012|015)\d{8}$/.test(value);

    if (value.length > 0) {
        if (isValid) {
            input.removeClass('is-invalid').addClass('is-valid');
            input.siblings('.invalid-feedback').hide();
        } else {
            input.removeClass('is-valid').addClass('is-invalid');
            input.siblings('.invalid-feedback').text('رقم الموبايل غير صحيح').show();
        }
    } else {
        input.removeClass('is-valid is-invalid');
        input.siblings('.invalid-feedback').hide();
    }

    return isValid;
}

/**
 * Validate Tax Registration Number
 */
function validateTaxRegistrationNumber(input) {
    const value = input.val().replace(/\s/g, '');
    const isValid = /^\d{9}$/.test(value);

    if (value.length > 0) {
        if (isValid) {
            input.removeClass('is-invalid').addClass('is-valid');
            input.siblings('.invalid-feedback').hide();
        } else {
            input.removeClass('is-valid').addClass('is-invalid');
            input.siblings('.invalid-feedback').text('رقم التسجيل الضريبي يجب أن يكون 9 أرقام').show();
        }
    } else {
        input.removeClass('is-valid is-invalid');
        input.siblings('.invalid-feedback').hide();
    }

    return isValid;
}

/**
 * Initialize Date Pickers
 */
function initializeDatePickers() {
    $('input[type="date"]').each(function() {
        // Set max date to today for past dates
        if ($(this).hasClass('past-date')) {
            $(this).attr('max', new Date().toISOString().split('T')[0]);
        }
    });
}

/**
 * Initialize Number Formatting
 */
function initializeNumberFormatting() {
    // Format currency inputs
    $('input[name="daily_revenue"]').on('input', function() {
        formatCurrency($(this));
    });

    // Format number inputs
    $('input[type="number"]').on('input', function() {
        const value = $(this).val();
        if (value < 0) {
            $(this).val(0);
        }
    });
}

/**
 * Format Currency Input
 */
function formatCurrency(input) {
    let value = input.val().replace(/[^\d.]/g, '');
    if (value) {
        // Format with commas
        value = parseFloat(value).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }
    input.val(value);
}

/**
 * Show Loading Spinner
 */
function showLoading(element) {
    const spinner = '<div class="spinner-border spinner-border-custom text-primary" role="status"><span class="visually-hidden">Loading...</span></div>';
    element.html(spinner);
}

/**
 * Hide Loading Spinner
 */
function hideLoading(element, originalContent) {
    element.html(originalContent);
}

/**
 * Show Success Message
 */
function showSuccessMessage(message) {
    showAlert(message, 'success');
}

/**
 * Show Error Message
 */
function showErrorMessage(message) {
    showAlert(message, 'danger');
}

/**
 * Show Alert Message
 */
function showAlert(message, type) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // Remove existing alerts
    $('.alert').remove();

    // Add new alert at the top of the container
    $('.container').first().prepend(alertHtml);

    // Auto-hide after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

/**
 * Confirm Delete Action
 */
function confirmDelete(message, callback) {
    if (confirm(message || 'هل أنت متأكد من الحذف؟')) {
        callback();
    }
}

/**
 * Generate Reference Number
 */
function generateReferenceNumber(date, sectorId, governorateId, campaignNumber, serial) {
    const formattedDate = date.replace(/-/g, '-');
    return `${formattedDate}-${sectorId}-${String(governorateId).padStart(2, '0')}-${String(campaignNumber).padStart(3, '0')}-${String(serial).padStart(2, '0')}`;
}

/**
 * Export Table to CSV
 */
function exportTableToCSV(tableId, filename) {
    const csv = [];
    const rows = document.querySelectorAll(`#${tableId} tr`);

    for (let i = 0; i < rows.length; i++) {
        const row = [];
        const cols = rows[i].querySelectorAll('td, th');

        for (let j = 0; j < cols.length; j++) {
            row.push(cols[j].innerText);
        }

        csv.push(row.join(','));
    }

    downloadCSV(csv.join('\n'), filename);
}

/**
 * Download CSV File
 */
function downloadCSV(csv, filename) {
    const csvFile = new Blob([csv], { type: 'text/csv' });
    const downloadLink = document.createElement('a');

    downloadLink.download = filename;
    downloadLink.href = window.URL.createObjectURL(csvFile);
    downloadLink.style.display = 'none';

    document.body.appendChild(downloadLink);
    downloadLink.click();
    document.body.removeChild(downloadLink);
}