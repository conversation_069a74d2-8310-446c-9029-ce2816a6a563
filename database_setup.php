<?php
/**
 * Database Setup Script
 * نظام تسجيل حملات الحصر الضريبي
 *
 * This script creates all necessary database tables and inserts initial data
 */

require_once 'config/database.php';

function setupDatabase() {
    global $pdo;

    try {
        // Create Sectors table (القطاعات)
        $sql = "
        CREATE TABLE IF NOT EXISTS sectors (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            code VARCHAR(10) NOT NULL UNIQUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $pdo->exec($sql);

        // Create Governorates table (المحافظات)
        $sql = "
        CREATE TABLE IF NOT EXISTS governorates (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            code VARCHAR(10) NOT NULL UNIQUE,
            sector_id INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (sector_id) REFERENCES sectors(id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        $pdo->exec($sql);

        // Create Tax Offices table (مأموريات الضرائب)
        $sql = "
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='tax_offices' AND xtype='U')
        CREATE TABLE tax_offices (
            id INT IDENTITY(1,1) PRIMARY KEY,
            name NVARCHAR(255) NOT NULL,
            type VARCHAR(50) NOT NULL, -- 'income' or 'vat'
            governorate_id INT,
            created_at DATETIME DEFAULT GETDATE(),
            updated_at DATETIME DEFAULT GETDATE(),
            FOREIGN KEY (governorate_id) REFERENCES governorates(id)
        )";
        $pdo->exec($sql);

        // Create Activity Types table (أنواع الأنشطة)
        $sql = "
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='activity_types' AND xtype='U')
        CREATE TABLE activity_types (
            id INT IDENTITY(1,1) PRIMARY KEY,
            name NVARCHAR(255) NOT NULL,
            category NVARCHAR(100),
            created_at DATETIME DEFAULT GETDATE(),
            updated_at DATETIME DEFAULT GETDATE()
        )";
        $pdo->exec($sql);

        // Create Campaigns table (الحملات)
        $sql = "
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='campaigns' AND xtype='U')
        CREATE TABLE campaigns (
            id INT IDENTITY(1,1) PRIMARY KEY,
            reference_number VARCHAR(50) NOT NULL UNIQUE,
            campaign_date DATE NOT NULL,
            sector_id INT NOT NULL,
            governorate_id INT NOT NULL,
            campaign_number INT NOT NULL,
            serial_number INT NOT NULL,

            -- Business Information
            business_name NVARCHAR(255),
            owner_name NVARCHAR(255),
            phone VARCHAR(20),
            national_id VARCHAR(14),
            mobile VARCHAR(11),
            activity_nature NVARCHAR(255),
            detailed_activity_type NVARCHAR(255),
            activity_start_date DATE,
            employees_count INT,

            -- Unit Information
            unit_number VARCHAR(50),
            building_number VARCHAR(50),
            street_name NVARCHAR(255),
            district NVARCHAR(255),
            unit_governorate_id INT,

            -- Tax Registration Status
            is_registered BIT DEFAULT 0,
            tax_registration_number VARCHAR(9),
            income_tax_office_id INT,
            is_vat_registered BIT DEFAULT 0,
            vat_tax_office_id INT,

            -- Electronic Invoice and Receipt
            invoice_registered BIT DEFAULT 0,
            invoice_required BIT DEFAULT 0,
            invoice_issued BIT DEFAULT 0,
            invoice_notes NTEXT,
            receipt_registered BIT DEFAULT 0,
            receipt_required BIT DEFAULT 0,
            receipt_issued BIT DEFAULT 0,
            receipt_notes NTEXT,

            -- Additional Information
            daily_revenue DECIMAL(15,2),
            has_other_activities BIT DEFAULT 0,
            other_activities_notes NTEXT,
            previous_visit BIT DEFAULT 0,
            previous_visit_date DATE,
            property_type VARCHAR(20), -- 'rent' or 'owned'
            original_owner_name NVARCHAR(255),
            owner_national_id VARCHAR(14),
            property_notes NTEXT,

            -- Attachments
            attachments NTEXT,

            created_at DATETIME DEFAULT GETDATE(),
            updated_at DATETIME DEFAULT GETDATE(),

            FOREIGN KEY (sector_id) REFERENCES sectors(id),
            FOREIGN KEY (governorate_id) REFERENCES governorates(id),
            FOREIGN KEY (unit_governorate_id) REFERENCES governorates(id),
            FOREIGN KEY (income_tax_office_id) REFERENCES tax_offices(id),
            FOREIGN KEY (vat_tax_office_id) REFERENCES tax_offices(id)
        )";
        $pdo->exec($sql);

        // Create Activity Logs table (سجل الأنشطة)
        $sql = "
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='activity_logs' AND xtype='U')
        CREATE TABLE activity_logs (
            id INT IDENTITY(1,1) PRIMARY KEY,
            action NVARCHAR(255) NOT NULL,
            details NTEXT,
            user_id INT,
            created_at DATETIME DEFAULT GETDATE()
        )";
        $pdo->exec($sql);

        // Insert initial data
        insertInitialData();

        echo "Database setup completed successfully!";

    } catch(PDOException $e) {
        echo "Error setting up database: " . $e->getMessage();
    }
}

function insertInitialData() {
    global $pdo;

    // Insert Sectors
    $sectors = [
        ['name' => 'القاهرة الكبرى', 'code' => 'CGC'],
        ['name' => 'غرب الدلتا والإسكندرية ومطروح', 'code' => 'WDA'],
        ['name' => 'شرق الدلتا ومدن القناة وسيناء', 'code' => 'EDC'],
        ['name' => 'مصر العليا والبحر الأحمر والوادي الجديد', 'code' => 'UER']
    ];

    foreach ($sectors as $sector) {
        $sql = "IF NOT EXISTS (SELECT 1 FROM sectors WHERE code = ?)
                INSERT INTO sectors (name, code) VALUES (?, ?)";
        $pdo->prepare($sql)->execute([$sector['code'], $sector['name'], $sector['code']]);
    }

    // Insert Governorates (محافظات جمهورية مصر العربية)
    $governorates = [
        // القاهرة الكبرى
        ['name' => 'القاهرة', 'code' => 'CAI', 'sector_code' => 'CGC'],
        ['name' => 'الجيزة', 'code' => 'GIZ', 'sector_code' => 'CGC'],
        ['name' => 'القليوبية', 'code' => 'QAL', 'sector_code' => 'CGC'],

        // غرب الدلتا والإسكندرية ومطروح
        ['name' => 'الإسكندرية', 'code' => 'ALX', 'sector_code' => 'WDA'],
        ['name' => 'البحيرة', 'code' => 'BEH', 'sector_code' => 'WDA'],
        ['name' => 'مطروح', 'code' => 'MAT', 'sector_code' => 'WDA'],
        ['name' => 'كفر الشيخ', 'code' => 'KFS', 'sector_code' => 'WDA'],
        ['name' => 'الغربية', 'code' => 'GHR', 'sector_code' => 'WDA'],
        ['name' => 'المنوفية', 'code' => 'MNF', 'sector_code' => 'WDA'],

        // شرق الدلتا ومدن القناة وسيناء
        ['name' => 'الدقهلية', 'code' => 'DAK', 'sector_code' => 'EDC'],
        ['name' => 'الشرقية', 'code' => 'SHR', 'sector_code' => 'EDC'],
        ['name' => 'دمياط', 'code' => 'DUM', 'sector_code' => 'EDC'],
        ['name' => 'بورسعيد', 'code' => 'PTS', 'sector_code' => 'EDC'],
        ['name' => 'الإسماعيلية', 'code' => 'ISM', 'sector_code' => 'EDC'],
        ['name' => 'السويس', 'code' => 'SUZ', 'sector_code' => 'EDC'],
        ['name' => 'شمال سيناء', 'code' => 'SIN', 'sector_code' => 'EDC'],
        ['name' => 'جنوب سيناء', 'code' => 'JAN', 'sector_code' => 'EDC'],

        // مصر العليا والبحر الأحمر والوادي الجديد
        ['name' => 'الفيوم', 'code' => 'FAY', 'sector_code' => 'UER'],
        ['name' => 'بني سويف', 'code' => 'BNS', 'sector_code' => 'UER'],
        ['name' => 'المنيا', 'code' => 'MIN', 'sector_code' => 'UER'],
        ['name' => 'أسيوط', 'code' => 'AST', 'sector_code' => 'UER'],
        ['name' => 'سوهاج', 'code' => 'SOH', 'sector_code' => 'UER'],
        ['name' => 'قنا', 'code' => 'QEN', 'sector_code' => 'UER'],
        ['name' => 'الأقصر', 'code' => 'LUX', 'sector_code' => 'UER'],
        ['name' => 'أسوان', 'code' => 'ASW', 'sector_code' => 'UER'],
        ['name' => 'البحر الأحمر', 'code' => 'SEA', 'sector_code' => 'UER'],
        ['name' => 'الوادي الجديد', 'code' => 'WAD', 'sector_code' => 'UER']
    ];

    foreach ($governorates as $gov) {
        // Get sector ID
        $sector = $pdo->prepare("SELECT id FROM sectors WHERE code = ?");
        $sector->execute([$gov['sector_code']]);
        $sector_id = $sector->fetchColumn();

        if ($sector_id) {
            $sql = "IF NOT EXISTS (SELECT 1 FROM governorates WHERE code = ?)
                    INSERT INTO governorates (name, code, sector_id) VALUES (?, ?, ?)";
            $pdo->prepare($sql)->execute([$gov['code'], $gov['name'], $gov['code'], $sector_id]);
        }
    }

    // Insert sample activity types
    $activity_types = [
        ['name' => 'تجارة تجزئة', 'category' => 'تجاري'],
        ['name' => 'تجارة جملة', 'category' => 'تجاري'],
        ['name' => 'مطعم', 'category' => 'خدمي'],
        ['name' => 'كافيه', 'category' => 'خدمي'],
        ['name' => 'صيدلية', 'category' => 'طبي'],
        ['name' => 'عيادة طبية', 'category' => 'طبي'],
        ['name' => 'ورشة إصلاح', 'category' => 'صناعي'],
        ['name' => 'مخبز', 'category' => 'غذائي'],
        ['name' => 'محل حلاقة', 'category' => 'خدمي'],
        ['name' => 'مكتب محاماة', 'category' => 'مهني']
    ];

    foreach ($activity_types as $type) {
        $sql = "IF NOT EXISTS (SELECT 1 FROM activity_types WHERE name = ?)
                INSERT INTO activity_types (name, category) VALUES (?, ?)";
        $pdo->prepare($sql)->execute([$type['name'], $type['name'], $type['category']]);
    }
}

// Run setup if called directly
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    setupDatabase();
}
?>