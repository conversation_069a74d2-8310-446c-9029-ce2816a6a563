اريد انشاء تطبيق ويب
ذات واجهة عصرية وتحسين تجربة المستخدم

بالمواصفات التالية
قواعد البيانات ms sql server
html
css
php

قم بتحليل ملف mapping table.xcls بكل الشيتات الموجودة بداخله لانها جداول مساعدة في عمليات التسجيل

الغرض من البرنامج
البرنامج يقوم بتسجيل نتائج حملات الحصر بالتعاون مع مباحث التهرب الضريبي 
مع امكانية استخراج تقارير مفصلة ورسوم بياناية ومؤشرات الاداء والتحليل الاحصائي
ملف منفصلة واستخدام كود نظيف
امكانية استيراد ملفات CSV 
ملف اعداد قاعدة البيانات database_setup.php
ملف لكل جدول من الجداول المساعدة مع امكانية التعديل والاضافة
لوجو البرنامج logo-nowords.png
انشاء ملف لتوثيق البرنامج

تفاصيل البيانات المطلوب تسجيلها
بيانات الحملة
•	تاريخ الحملة: (يوم، شهر، سنة)
•	القطاع:( القاهرة الكبرى / غرب الدلتا والإسكندرية ومطروح / شرق الدلتا ومدن القناه وسيناء / مصر العليا والبحر الاحمر والوادي الجديد )
•	رقم الحملة:
•	المسلسل:
•	محافظة الحملة:(محافظات جمهورية مصر العربية) الاختيار من قائمة
الرقم المرجعي : عبارة عن 16 رقم من بيانات الحملة مثال 2025-09-09-1-01-001-02
________________________________________
بيانات النشاط وصاحب النشاط
•	الاسم التجاري:
•	اسم صاحب النشاط:
•	رقم التليفون:
•	الرقم القومي: (14 رقم)
•	رقم الموبايل: (11 رقم)
•	طبيعة النشاط:
•	نوع النشاط التجاري التفصيلي:
•	تاريخ بدء النشاط: (يوم، شهر، سنة)
•	عدد العاملين:
________________________________________
بيانات الوحدة
•	رقم الوحدة:
•	رقم العقار:
•	اسم الشارع:
•	الحي:
•	المحافظة / المدينة: (محافظات جمهورية مصر العربية) الاختيار من قائمة
________________________________________
موقف التسجيل الضريبي
•	هل النشاط مسجل؟ (نعم / لا)
•	رقم التسجيل الضريبي:(9 ارقام)
•	مأمورية الدخل التابع لها:(الاختيار من قائمة مأموريات الدخل)
•	هل النشاط مسجل في القيمة المضافة؟ (نعم / لا)
•	مأمورية القيمة المضافة:(الاختيار من قائمة مأموريات ق.م)
________________________________________
الفاتورة والإيصال الإلكتروني
 
•	مسجل فاتورة؟ (نعم / لا)
•	ملزم فاتورة؟ (نعم / لا)
•	يصدر فاتورة؟ (نعم / لا)
•	ملاحظات الفاتورة:
•	مسجل إيصال؟ (نعم / لا)
•	ملزم إيصال؟ (نعم / لا)
•	يصدر إيصال؟ (نعم / لا)
•	ملاحظات الإيصال:
 
________________________________________
 
ملاحظات إضافية
•	الإيرادات اليومية:
•	هل يوجد أنشطة أخرى غير مسجلة؟ (نعم / لا)
•	ملاحظات الأنشطة الأخرى:
•	هل تم المرور على العين من قبل؟ (نعم / لا)
•	تاريخ المرور السابق: (يوم، شهر، سنة).
•	نوعية العين: (إيجار / تمليك)
•	اسم مالك العين الأصلي:
•	الرقم القومي لمالك العين:(14 رقم)
•	ملاحظات حول العين (وصف) وصاحب النشاط:
________________________________________
المرفقات
•	قائمة المرفقات.
