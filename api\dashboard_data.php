<?php
/**
 * Dashboard Data API
 * Returns statistics for the dashboard
 */

header('Content-Type: application/json');
require_once '../config/database.php';

try {
    // Get total campaigns
    $total_campaigns = getRecord("SELECT COUNT(*) as count FROM campaigns")['count'] ?? 0;

    // Get registered activities
    $registered_activities = getRecord("SELECT COUNT(*) as count FROM campaigns WHERE is_registered = 1")['count'] ?? 0;

    // Get unregistered activities
    $unregistered_activities = getRecord("SELECT COUNT(*) as count FROM campaigns WHERE is_registered = 0")['count'] ?? 0;

    // Get covered governorates
    $covered_governorates = getRecord("
        SELECT COUNT(DISTINCT governorate_id) as count
        FROM campaigns
        WHERE governorate_id IS NOT NULL
    ")['count'] ?? 0;

    $response = [
        'success' => true,
        'total_campaigns' => $total_campaigns,
        'registered_activities' => $registered_activities,
        'unregistered_activities' => $unregistered_activities,
        'covered_governorates' => $covered_governorates
    ];

    echo json_encode($response, JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    $response = [
        'success' => false,
        'error' => $e->getMessage()
    ];

    echo json_encode($response, JSON_UNESCAPED_UNICODE);
}
?>