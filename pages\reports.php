<?php
session_start();
require_once '../config/database.php';

// Get filter parameters
$sector_filter = $_GET['sector'] ?? '';
$governorate_filter = $_GET['governorate'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

// Build WHERE clause
$where_conditions = [];
$params = [];

if ($sector_filter) {
    $where_conditions[] = "c.sector_id = ?";
    $params[] = $sector_filter;
}

if ($governorate_filter) {
    $where_conditions[] = "c.governorate_id = ?";
    $params[] = $governorate_filter;
}

if ($date_from) {
    $where_conditions[] = "c.campaign_date >= ?";
    $params[] = $date_from;
}

if ($date_to) {
    $where_conditions[] = "c.campaign_date <= ?";
    $params[] = $date_to;
}

$where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get statistics
$stats_sql = "
    SELECT
        COUNT(*) as total_campaigns,
        SUM(CASE WHEN is_registered = 1 THEN 1 ELSE 0 END) as registered_count,
        SUM(CASE WHEN is_registered = 0 THEN 1 ELSE 0 END) as unregistered_count,
        SUM(CASE WHEN is_vat_registered = 1 THEN 1 ELSE 0 END) as vat_registered_count,
        AVG(CAST(daily_revenue as FLOAT)) as avg_daily_revenue,
        SUM(CAST(daily_revenue as FLOAT)) as total_revenue
    FROM campaigns c
    $where_clause
";

$stats = getRecord($stats_sql, $params);

// Get campaigns by sector
$sector_stats_sql = "
    SELECT
        s.name as sector_name,
        COUNT(*) as campaign_count,
        SUM(CASE WHEN c.is_registered = 1 THEN 1 ELSE 0 END) as registered_count
    FROM campaigns c
    INNER JOIN sectors s ON c.sector_id = s.id
    $where_clause
    GROUP BY s.id, s.name
    ORDER BY campaign_count DESC
";

$sector_stats = getRecords($sector_stats_sql, $params);

// Get campaigns by governorate
$governorate_stats_sql = "
    SELECT
        g.name as governorate_name,
        COUNT(*) as campaign_count,
        SUM(CASE WHEN c.is_registered = 1 THEN 1 ELSE 0 END) as registered_count
    FROM campaigns c
    INNER JOIN governorates g ON c.governorate_id = g.id
    $where_clause
    GROUP BY g.id, g.name
    ORDER BY campaign_count DESC
";

$governorate_stats = getRecords($governorate_stats_sql, $params);

// Get monthly statistics
$monthly_stats_sql = "
    SELECT
        YEAR(campaign_date) as year,
        MONTH(campaign_date) as month,
        COUNT(*) as campaign_count,
        SUM(CASE WHEN is_registered = 1 THEN 1 ELSE 0 END) as registered_count
    FROM campaigns c
    $where_clause
    GROUP BY YEAR(campaign_date), MONTH(campaign_date)
    ORDER BY year DESC, month DESC
";

$monthly_stats = getRecords($monthly_stats_sql, $params);

// Get sectors and governorates for filters
$sectors = getRecords("SELECT id, name FROM sectors ORDER BY name");
$governorates = getRecords("SELECT id, name FROM governorates ORDER BY name");
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير والإحصائيات - نظام تسجيل حملات الحصر الضريبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <img src="../logo-nowords.png" alt="Logo" height="40" class="me-2">
                نظام تسجيل حملات الحصر الضريبي
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="add_campaign.php">
                            <i class="fas fa-plus"></i> إضافة حملة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="campaigns_list.php">
                            <i class="fas fa-list"></i> قائمة الحملات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="reports.php">
                            <i class="fas fa-chart-bar"></i> التقارير
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Filters -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-filter"></i> فلاتر التقارير
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="sector" class="form-label">القطاع</label>
                        <select class="form-select" id="sector" name="sector">
                            <option value="">جميع القطاعات</option>
                            <?php foreach ($sectors as $sector): ?>
                                <option value="<?= $sector['id'] ?>" <?= $sector_filter == $sector['id'] ? 'selected' : '' ?>>
                                    <?= $sector['name'] ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="governorate" class="form-label">المحافظة</label>
                        <select class="form-select" id="governorate" name="governorate">
                            <option value="">جميع المحافظات</option>
                            <?php foreach ($governorates as $gov): ?>
                                <option value="<?= $gov['id'] ?>" <?= $governorate_filter == $gov['id'] ? 'selected' : '' ?>>
                                    <?= $gov['name'] ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="date_from" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="<?= $date_from ?>">
                    </div>
                    <div class="col-md-3">
                        <label for="date_to" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="<?= $date_to ?>">
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> تطبيق الفلاتر
                        </button>
                        <a href="reports.php" class="btn btn-secondary">
                            <i class="fas fa-undo"></i> إعادة تعيين
                        </a>
                        <button type="button" class="btn btn-success" onclick="exportToCSV()">
                            <i class="fas fa-download"></i> تصدير CSV
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0"><?= number_format($stats['total_campaigns'] ?? 0) ?></h4>
                                <p class="mb-0">إجمالي الحملات</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-clipboard-list fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0"><?= number_format($stats['registered_count'] ?? 0) ?></h4>
                                <p class="mb-0">الأنشطة المسجلة</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-check-circle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0"><?= number_format($stats['unregistered_count'] ?? 0) ?></h4>
                                <p class="mb-0">الأنشطة غير المسجلة</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0"><?= number_format($stats['total_revenue'] ?? 0, 2) ?></h4>
                                <p class="mb-0">إجمالي الإيرادات</p>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-money-bill-wave fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-pie"></i> توزيع الحملات حسب القطاع
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="sectorChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-bar"></i> نسبة التسجيل الضريبي
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="registrationChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tables Row -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-table"></i> إحصائيات القطاعات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>القطاع</th>
                                        <th>عدد الحملات</th>
                                        <th>المسجلة</th>
                                        <th>نسبة التسجيل</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($sector_stats as $sector): ?>
                                        <tr>
                                            <td><?= $sector['sector_name'] ?></td>
                                            <td><?= number_format($sector['campaign_count']) ?></td>
                                            <td><?= number_format($sector['registered_count']) ?></td>
                                            <td>
                                                <?php
                                                $percentage = $sector['campaign_count'] > 0 ?
                                                    ($sector['registered_count'] / $sector['campaign_count']) * 100 : 0;
                                                echo number_format($percentage, 1) . '%';
                                                ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-table"></i> أعلى المحافظات
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>المحافظة</th>
                                        <th>عدد الحملات</th>
                                        <th>المسجلة</th>
                                        <th>نسبة التسجيل</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $top_governorates = array_slice($governorate_stats, 0, 10);
                                    foreach ($top_governorates as $gov):
                                    ?>
                                        <tr>
                                            <td><?= $gov['governorate_name'] ?></td>
                                            <td><?= number_format($gov['campaign_count']) ?></td>
                                            <td><?= number_format($gov['registered_count']) ?></td>
                                            <td>
                                                <?php
                                                $percentage = $gov['campaign_count'] > 0 ?
                                                    ($gov['registered_count'] / $gov['campaign_count']) * 100 : 0;
                                                echo number_format($percentage, 1) . '%';
                                                ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monthly Trend Chart -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-line"></i> اتجاه الحملات الشهري
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="monthlyTrendChart" height="100"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p>&copy; 2025 نظام تسجيل حملات الحصر الضريبي - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="../assets/js/main.js"></script>

    <script>
        // Sector Chart
        const sectorData = <?= json_encode($sector_stats, JSON_UNESCAPED_UNICODE) ?>;
        const sectorLabels = sectorData.map(item => item.sector_name);
        const sectorValues = sectorData.map(item => parseInt(item.campaign_count));

        const sectorChart = new Chart(document.getElementById('sectorChart'), {
            type: 'pie',
            data: {
                labels: sectorLabels,
                datasets: [{
                    data: sectorValues,
                    backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Registration Chart
        const registrationChart = new Chart(document.getElementById('registrationChart'), {
            type: 'doughnut',
            data: {
                labels: ['مسجلة', 'غير مسجلة'],
                datasets: [{
                    data: [<?= $stats['registered_count'] ?? 0 ?>, <?= $stats['unregistered_count'] ?? 0 ?>],
                    backgroundColor: ['#28a745', '#ffc107']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Monthly Trend Chart
        const monthlyData = <?= json_encode($monthly_stats, JSON_UNESCAPED_UNICODE) ?>;
        const monthlyLabels = monthlyData.map(item => `${item.month}/${item.year}`);
        const monthlyCampaigns = monthlyData.map(item => parseInt(item.campaign_count));
        const monthlyRegistered = monthlyData.map(item => parseInt(item.registered_count));

        const monthlyTrendChart = new Chart(document.getElementById('monthlyTrendChart'), {
            type: 'line',
            data: {
                labels: monthlyLabels.reverse(),
                datasets: [{
                    label: 'إجمالي الحملات',
                    data: monthlyCampaigns.reverse(),
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                }, {
                    label: 'الحملات المسجلة',
                    data: monthlyRegistered.reverse(),
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        position: 'top'
                    }
                }
            }
        });

        // Export to CSV function
        function exportToCSV() {
            const params = new URLSearchParams(window.location.search);
            params.set('export', 'csv');
            window.location.href = 'export_csv.php?' + params.toString();
        }
    </script>
</body>
</html>