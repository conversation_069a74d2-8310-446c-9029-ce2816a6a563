<?php
session_start();
require_once '../config/database.php';

$import_results = [];
$errors = [];
$success_count = 0;
$error_count = 0;

if ($_POST && isset($_FILES['csv_file'])) {
    $file = $_FILES['csv_file'];

    if ($file['error'] === UPLOAD_ERR_OK) {
        $file_path = $file['tmp_name'];

        if (($handle = fopen($file_path, "r")) !== FALSE) {
            $header = fgetcsv($handle); // Skip header row
            $row_number = 1;

            while (($data = fgetcsv($handle)) !== FALSE) {
                $row_number++;

                try {
                    // Validate required fields
                    if (empty($data[0]) || empty($data[1]) || empty($data[2])) {
                        throw new Exception("الحقول المطلوبة مفقودة");
                    }

                    // Map CSV columns to database fields
                    $campaign_data = [
                        'campaign_date' => $data[0],
                        'sector_id' => $data[1],
                        'governorate_id' => $data[2],
                        'campaign_number' => $data[3] ?? '',
                        'serial_number' => $data[4] ?? '',
                        'business_name' => $data[5] ?? '',
                        'owner_name' => $data[6] ?? '',
                        'phone' => $data[7] ?? '',
                        'national_id' => $data[8] ?? '',
                        'mobile' => $data[9] ?? '',
                        'activity_nature' => $data[10] ?? '',
                        'detailed_activity_type' => $data[11] ?? '',
                        'activity_start_date' => $data[12] ?? null,
                        'employees_count' => $data[13] ?? 0,
                        'unit_number' => $data[14] ?? '',
                        'building_number' => $data[15] ?? '',
                        'street_name' => $data[16] ?? '',
                        'district' => $data[17] ?? '',
                        'unit_governorate_id' => $data[18] ?? null,
                        'is_registered' => ($data[19] ?? '') === '1' ? 1 : 0,
                        'tax_registration_number' => $data[20] ?? '',
                        'income_tax_office_id' => $data[21] ?? null,
                        'is_vat_registered' => ($data[22] ?? '') === '1' ? 1 : 0,
                        'vat_tax_office_id' => $data[23] ?? null,
                        'daily_revenue' => $data[24] ?? 0,
                        'property_type' => $data[25] ?? '',
                        'original_owner_name' => $data[26] ?? '',
                        'owner_national_id' => $data[27] ?? '',
                        'property_notes' => $data[28] ?? '',
                        'attachments' => $data[29] ?? ''
                    ];

                    // Validate data
                    if (!empty($campaign_data['national_id']) && !validateEgyptianNationalId($campaign_data['national_id'])) {
                        throw new Exception("رقم قومي غير صحيح: " . $campaign_data['national_id']);
                    }

                    if (!empty($campaign_data['mobile']) && !validateEgyptianMobile($campaign_data['mobile'])) {
                        throw new Exception("رقم موبايل غير صحيح: " . $campaign_data['mobile']);
                    }

                    if (!empty($campaign_data['tax_registration_number']) && !validateTaxRegistrationNumber($campaign_data['tax_registration_number'])) {
                        throw new Exception("رقم تسجيل ضريبي غير صحيح: " . $campaign_data['tax_registration_number']);
                    }

                    // Generate reference number
                    $reference_number = generateReferenceNumber(
                        $campaign_data['campaign_date'],
                        $campaign_data['sector_id'],
                        $campaign_data['governorate_id'],
                        $campaign_data['campaign_number'],
                        $campaign_data['serial_number']
                    );

                    // Insert into database
                    $sql = "INSERT INTO campaigns (
                        reference_number, campaign_date, sector_id, governorate_id,
                        campaign_number, serial_number, business_name, owner_name,
                        phone, national_id, mobile, activity_nature, detailed_activity_type,
                        activity_start_date, employees_count, unit_number, building_number,
                        street_name, district, unit_governorate_id, is_registered,
                        tax_registration_number, income_tax_office_id, is_vat_registered,
                        vat_tax_office_id, daily_revenue, property_type, original_owner_name,
                        owner_national_id, property_notes, attachments
                    ) VALUES (
                        ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                    )";

                    $params = [
                        $reference_number,
                        $campaign_data['campaign_date'],
                        $campaign_data['sector_id'],
                        $campaign_data['governorate_id'],
                        $campaign_data['campaign_number'],
                        $campaign_data['serial_number'],
                        $campaign_data['business_name'],
                        $campaign_data['owner_name'],
                        $campaign_data['phone'],
                        $campaign_data['national_id'],
                        $campaign_data['mobile'],
                        $campaign_data['activity_nature'],
                        $campaign_data['detailed_activity_type'],
                        $campaign_data['activity_start_date'],
                        $campaign_data['employees_count'],
                        $campaign_data['unit_number'],
                        $campaign_data['building_number'],
                        $campaign_data['street_name'],
                        $campaign_data['district'],
                        $campaign_data['unit_governorate_id'],
                        $campaign_data['is_registered'],
                        $campaign_data['tax_registration_number'],
                        $campaign_data['income_tax_office_id'],
                        $campaign_data['is_vat_registered'],
                        $campaign_data['vat_tax_office_id'],
                        $campaign_data['daily_revenue'],
                        $campaign_data['property_type'],
                        $campaign_data['original_owner_name'],
                        $campaign_data['owner_national_id'],
                        $campaign_data['property_notes'],
                        $campaign_data['attachments']
                    ];

                    insertRecord($sql, $params);
                    $success_count++;

                    $import_results[] = [
                        'row' => $row_number,
                        'status' => 'success',
                        'message' => "تم استيراد الصف بنجاح - الرقم المرجعي: $reference_number"
                    ];

                } catch (Exception $e) {
                    $error_count++;
                    $import_results[] = [
                        'row' => $row_number,
                        'status' => 'error',
                        'message' => $e->getMessage()
                    ];
                }
            }

            fclose($handle);

            // Log the import activity
            logActivity('استيراد CSV', "تم استيراد $success_count صف بنجاح، $error_count خطأ");

        } else {
            $errors[] = "لا يمكن قراءة الملف";
        }
    } else {
        $errors[] = "خطأ في رفع الملف";
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استيراد ملف CSV - نظام تسجيل حملات الحصر الضريبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <img src="../logo-nowords.png" alt="Logo" height="40" class="me-2">
                نظام تسجيل حملات الحصر الضريبي
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="add_campaign.php">
                            <i class="fas fa-plus"></i> إضافة حملة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="campaigns_list.php">
                            <i class="fas fa-list"></i> قائمة الحملات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-chart-bar"></i> التقارير
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <?php if (!empty($errors)): ?>
            <?php foreach ($errors as $error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> <?= $error ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>

        <?php if (!empty($import_results)): ?>
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle"></i>
                تم الانتهاء من عملية الاستيراد: <?= $success_count ?> صف تم بنجاح، <?= $error_count ?> خطأ
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-upload"></i> استيراد ملف CSV
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="csv_file" class="form-label">اختر ملف CSV</label>
                                <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                                <div class="form-text">يجب أن يكون الملف بصيغة CSV ويحتوي على البيانات بالترتيب المحدد</div>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload"></i> استيراد البيانات
                            </button>
                            <a href="../index.php" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> العودة للرئيسية
                            </a>
                        </form>
                    </div>
                </div>

                <?php if (!empty($import_results)): ?>
                    <div class="card mt-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-list"></i> نتائج الاستيراد
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>رقم الصف</th>
                                            <th>الحالة</th>
                                            <th>الرسالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($import_results as $result): ?>
                                            <tr>
                                                <td><?= $result['row'] ?></td>
                                                <td>
                                                    <?php if ($result['status'] === 'success'): ?>
                                                        <span class="badge bg-success">نجح</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">خطأ</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?= $result['message'] ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle"></i> تعليمات الاستيراد
                        </h5>
                    </div>
                    <div class="card-body">
                        <h6>تنسيق الملف المطلوب:</h6>
                        <ul class="list-unstyled">
                            <li><strong>1.</strong> تاريخ الحملة (YYYY-MM-DD)</li>
                            <li><strong>2.</strong> رقم القطاع</li>
                            <li><strong>3.</strong> رقم المحافظة</li>
                            <li><strong>4.</strong> رقم الحملة</li>
                            <li><strong>5.</strong> المسلسل</li>
                            <li><strong>6.</strong> الاسم التجاري</li>
                            <li><strong>7.</strong> اسم صاحب النشاط</li>
                            <li><strong>8.</strong> رقم التليفون</li>
                            <li><strong>9.</strong> الرقم القومي</li>
                            <li><strong>10.</strong> رقم الموبايل</li>
                            <li><strong>11.</strong> طبيعة النشاط</li>
                            <li><strong>12.</strong> نوع النشاط التفصيلي</li>
                            <li><strong>13.</strong> تاريخ بدء النشاط</li>
                            <li><strong>14.</strong> عدد العاملين</li>
                            <li><strong>15.</strong> رقم الوحدة</li>
                            <li><strong>16.</strong> رقم العقار</li>
                            <li><strong>17.</strong> اسم الشارع</li>
                            <li><strong>18.</strong> الحي</li>
                            <li><strong>19.</strong> رقم محافظة الوحدة</li>
                            <li><strong>20.</strong> مسجل؟ (1 أو 0)</li>
                            <li><strong>21.</strong> رقم التسجيل الضريبي</li>
                            <li><strong>22.</strong> رقم مأمورية الدخل</li>
                            <li><strong>23.</strong> مسجل قيمة مضافة؟ (1 أو 0)</li>
                            <li><strong>24.</strong> رقم مأمورية القيمة المضافة</li>
                            <li><strong>25.</strong> الإيرادات اليومية</li>
                            <li><strong>26.</strong> نوعية العين</li>
                            <li><strong>27.</strong> اسم مالك العين</li>
                            <li><strong>28.</strong> رقم قومي مالك العين</li>
                            <li><strong>29.</strong> ملاحظات العين</li>
                            <li><strong>30.</strong> المرفقات</li>
                        </ul>

                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>تنبيه:</strong> الحقول الثلاثة الأولى (تاريخ الحملة، القطاع، المحافظة) مطلوبة
                        </div>

                        <a href="sample_template.csv" class="btn btn-outline-primary btn-sm" download>
                            <i class="fas fa-download"></i> تحميل نموذج CSV
                        </a>
                    </div>
                </div>

                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-bar"></i> إحصائيات سريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php
                        $total_campaigns = getRecord("SELECT COUNT(*) as count FROM campaigns")['count'] ?? 0;
                        $today_campaigns = getRecord("SELECT COUNT(*) as count FROM campaigns WHERE CAST(created_at as DATE) = CAST(GETDATE() as DATE)")['count'] ?? 0;
                        ?>
                        <div class="row text-center">
                            <div class="col-6">
                                <h4 class="text-primary"><?= number_format($total_campaigns) ?></h4>
                                <small>إجمالي الحملات</small>
                            </div>
                            <div class="col-6">
                                <h4 class="text-success"><?= number_format($today_campaigns) ?></h4>
                                <small>حملات اليوم</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p>&copy; 2025 نظام تسجيل حملات الحصر الضريبي - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../assets/js/main.js"></script>
</body>
</html>