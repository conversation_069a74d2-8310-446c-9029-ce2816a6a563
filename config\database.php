<?php
/**
 * Database Configuration File
 * نظام تسجيل حملات الحصر الضريبي
 */

// Database configuration
$db_config = [
    'server' => 'localhost',
    'database' => 'tax_campaigns_db',
    'username' => 'sa',
    'password' => '1975',
    'charset' => 'UTF-8'
];

try {
    // Create connection to SQL Server
    $dsn = "sqlsrv:Server={$db_config['server']};Database={$db_config['database']}";
    $pdo = new PDO($dsn, $db_config['username'], $db_config['password']);

    // Set PDO attributes
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

} catch(PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

/**
 * Execute SQL query
 */
function executeQuery($sql, $params = []) {
    global $pdo;
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch(PDOException $e) {
        throw new Exception("Query failed: " . $e->getMessage());
    }
}

/**
 * Get single record
 */
function getRecord($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->fetch();
}

/**
 * Get multiple records
 */
function getRecords($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->fetchAll();
}

/**
 * Insert record and return last insert ID
 */
function insertRecord($sql, $params = []) {
    executeQuery($sql, $params);
    global $pdo;
    return $pdo->lastInsertId();
}

/**
 * Update record
 */
function updateRecord($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->rowCount();
}

/**
 * Delete record
 */
function deleteRecord($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt->rowCount();
}

/**
 * Generate reference number for campaign
 * Format: YYYY-MM-DD-SECTOR-GOVERNORATE-CAMPAIGN-SERIAL
 */
function generateReferenceNumber($date, $sector_id, $governorate_id, $campaign_number, $serial) {
    return sprintf("%s-%d-%02d-%03d-%02d",
        $date,
        $sector_id,
        $governorate_id,
        $campaign_number,
        $serial
    );
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(strip_tags(trim($data)), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate Egyptian National ID
 */
function validateNationalId($id) {
    // Remove any spaces or dashes
    $id = preg_replace('/[\s-]/', '', $id);

    // Check if it's exactly 14 digits
    if (!preg_match('/^\d{14}$/', $id)) {
        return false;
    }

    // Additional validation logic can be added here
    return true;
}

/**
 * Validate Egyptian mobile number
 */
function validateMobileNumber($mobile) {
    // Remove any spaces, dashes, or plus signs
    $mobile = preg_replace('/[\s\-\+]/', '', $mobile);

    // Check if it starts with Egyptian mobile prefixes and is 11 digits
    if (preg_match('/^(010|011|012|015)\d{8}$/', $mobile)) {
        return true;
    }

    return false;
}

/**
 * Format date for display
 */
function formatDate($date, $format = 'Y-m-d') {
    if (empty($date)) return '';
    return date($format, strtotime($date));
}

/**
 * Get current timestamp
 */
function getCurrentTimestamp() {
    return date('Y-m-d H:i:s');
}

/**
 * Log activity
 */
function logActivity($action, $details = '', $user_id = null) {
    $sql = "INSERT INTO activity_logs (action, details, user_id, created_at) VALUES (?, ?, ?, ?)";
    $params = [$action, $details, $user_id, getCurrentTimestamp()];
    executeQuery($sql, $params);
}

/**
 * Check if table exists
 */
function tableExists($tableName) {
    global $pdo;
    try {
        $sql = "SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$tableName]);
        return $stmt->rowCount() > 0;
    } catch(PDOException $e) {
        return false;
    }
}

/**
 * Create database tables if they don't exist
 */
function createTables() {
    // This function will be called from database_setup.php
    require_once 'database_setup.php';
    setupDatabase();
}

// Initialize database tables if they don't exist
if (!tableExists('campaigns')) {
    createTables();
}
?>