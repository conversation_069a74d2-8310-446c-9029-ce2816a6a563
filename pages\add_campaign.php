<?php
session_start();
require_once '../config/database.php';

// Handle form submission
if ($_POST) {
    try {
        // Sanitize input data
        $data = sanitizeInput($_POST);

        // Generate reference number
        $reference_number = generateReferenceNumber(
            $data['campaign_date'],
            $data['sector_id'],
            $data['governorate_id'],
            $data['campaign_number'],
            $data['serial_number']
        );

        // Insert campaign data
        $sql = "INSERT INTO campaigns (
            reference_number, campaign_date, sector_id, governorate_id,
            campaign_number, serial_number, business_name, owner_name,
            phone, national_id, mobile, activity_nature, detailed_activity_type,
            activity_start_date, employees_count, unit_number, building_number,
            street_name, district, unit_governorate_id, is_registered,
            tax_registration_number, income_tax_office_id, is_vat_registered,
            vat_tax_office_id, invoice_registered, invoice_required, invoice_issued,
            invoice_notes, receipt_registered, receipt_required, receipt_issued,
            receipt_notes, daily_revenue, has_other_activities, other_activities_notes,
            previous_visit, previous_visit_date, property_type, original_owner_name,
            owner_national_id, property_notes, attachments
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
        )";

        $params = [
            $reference_number, $data['campaign_date'], $data['sector_id'], $data['governorate_id'],
            $data['campaign_number'], $data['serial_number'], $data['business_name'], $data['owner_name'],
            $data['phone'], $data['national_id'], $data['mobile'], $data['activity_nature'], $data['detailed_activity_type'],
            $data['activity_start_date'], $data['employees_count'], $data['unit_number'], $data['building_number'],
            $data['street_name'], $data['district'], $data['unit_governorate_id'], isset($data['is_registered']) ? 1 : 0,
            $data['tax_registration_number'], $data['income_tax_office_id'], isset($data['is_vat_registered']) ? 1 : 0,
            $data['vat_tax_office_id'], isset($data['invoice_registered']) ? 1 : 0, isset($data['invoice_required']) ? 1 : 0,
            isset($data['invoice_issued']) ? 1 : 0, $data['invoice_notes'], isset($data['receipt_registered']) ? 1 : 0,
            isset($data['receipt_required']) ? 1 : 0, isset($data['receipt_issued']) ? 1 : 0, $data['receipt_notes'],
            $data['daily_revenue'], isset($data['has_other_activities']) ? 1 : 0, $data['other_activities_notes'],
            isset($data['previous_visit']) ? 1 : 0, $data['previous_visit_date'], $data['property_type'],
            $data['original_owner_name'], $data['owner_national_id'], $data['property_notes'], $data['attachments']
        ];

        $campaign_id = insertRecord($sql, $params);

        // Log activity
        logActivity('إضافة حملة جديدة', "تم إضافة حملة برقم مرجعي: $reference_number");

        $success_message = "تم إضافة الحملة بنجاح برقم مرجعي: $reference_number";

    } catch (Exception $e) {
        $error_message = "حدث خطأ أثناء إضافة الحملة: " . $e->getMessage();
    }
}

// Get sectors for dropdown
$sectors = getRecords("SELECT id, name FROM sectors ORDER BY name");

// Get governorates for dropdown
$governorates = getRecords("SELECT id, name, sector_id FROM governorates ORDER BY name");

// Get tax offices for dropdown
$income_tax_offices = getRecords("SELECT id, name FROM tax_offices WHERE type = 'income' ORDER BY name");
$vat_tax_offices = getRecords("SELECT id, name FROM tax_offices WHERE type = 'vat' ORDER BY name");
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة حملة جديدة - نظام تسجيل حملات الحصر الضريبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="../index.php">
                <img src="../logo-nowords.png" alt="Logo" height="40" class="me-2">
                نظام تسجيل حملات الحصر الضريبي
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php">
                            <i class="fas fa-home"></i> الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="add_campaign.php">
                            <i class="fas fa-plus"></i> إضافة حملة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="campaigns_list.php">
                            <i class="fas fa-list"></i> قائمة الحملات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.php">
                            <i class="fas fa-chart-bar"></i> التقارير
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i> <?= $success_message ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle"></i> <?= $error_message ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-plus-circle"></i> إضافة حملة جديدة
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="campaign-form">
                            <!-- Campaign Information -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="fas fa-info-circle"></i> بيانات الحملة
                                    </h6>
                                </div>
                                <div class="col-md-3">
                                    <label for="campaign_date" class="form-label">تاريخ الحملة *</label>
                                    <input type="date" class="form-control past-date" id="campaign_date" name="campaign_date" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-3">
                                    <label for="sector_id" class="form-label">القطاع *</label>
                                    <select class="form-select" id="sector_id" name="sector_id" required>
                                        <option value="">اختر القطاع</option>
                                        <?php foreach ($sectors as $sector): ?>
                                            <option value="<?= $sector['id'] ?>"><?= $sector['name'] ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-3">
                                    <label for="governorate_id" class="form-label">محافظة الحملة *</label>
                                    <select class="form-select" id="governorate_id" name="governorate_id" required>
                                        <option value="">اختر المحافظة</option>
                                        <?php foreach ($governorates as $gov): ?>
                                            <option value="<?= $gov['id'] ?>" data-sector="<?= $gov['sector_id'] ?>"><?= $gov['name'] ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-3">
                                    <label for="campaign_number" class="form-label">رقم الحملة *</label>
                                    <input type="number" class="form-control" id="campaign_number" name="campaign_number" min="1" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-3">
                                    <label for="serial_number" class="form-label">المسلسل *</label>
                                    <input type="number" class="form-control" id="serial_number" name="serial_number" min="1" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-9">
                                    <label class="form-label">الرقم المرجعي</label>
                                    <input type="text" class="form-control" id="reference_number" readonly>
                                    <small class="form-text text-muted">سيتم توليد الرقم المرجعي تلقائياً</small>
                                </div>
                            </div>

                            <!-- Business Information -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="fas fa-store"></i> بيانات النشاط وصاحب النشاط
                                    </h6>
                                </div>
                                <div class="col-md-6">
                                    <label for="business_name" class="form-label">الاسم التجاري</label>
                                    <input type="text" class="form-control" id="business_name" name="business_name">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6">
                                    <label for="owner_name" class="form-label">اسم صاحب النشاط</label>
                                    <input type="text" class="form-control" id="owner_name" name="owner_name">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-4">
                                    <label for="phone" class="form-label">رقم التليفون</label>
                                    <input type="tel" class="form-control" id="phone" name="phone">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-4">
                                    <label for="national_id" class="form-label">الرقم القومي (14 رقم)</label>
                                    <input type="text" class="form-control" id="national_id" name="national_id" maxlength="14">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-4">
                                    <label for="mobile" class="form-label">رقم الموبايل (11 رقم)</label>
                                    <input type="tel" class="form-control" id="mobile" name="mobile" maxlength="11">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6">
                                    <label for="activity_nature" class="form-label">طبيعة النشاط</label>
                                    <input type="text" class="form-control" id="activity_nature" name="activity_nature">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6">
                                    <label for="detailed_activity_type" class="form-label">نوع النشاط التجاري التفصيلي</label>
                                    <input type="text" class="form-control" id="detailed_activity_type" name="detailed_activity_type">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6">
                                    <label for="activity_start_date" class="form-label">تاريخ بدء النشاط</label>
                                    <input type="date" class="form-control past-date" id="activity_start_date" name="activity_start_date">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6">
                                    <label for="employees_count" class="form-label">عدد العاملين</label>
                                    <input type="number" class="form-control" id="employees_count" name="employees_count" min="0">
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>

                            <!-- Unit Information -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="fas fa-building"></i> بيانات الوحدة
                                    </h6>
                                </div>
                                <div class="col-md-3">
                                    <label for="unit_number" class="form-label">رقم الوحدة</label>
                                    <input type="text" class="form-control" id="unit_number" name="unit_number">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-3">
                                    <label for="building_number" class="form-label">رقم العقار</label>
                                    <input type="text" class="form-control" id="building_number" name="building_number">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6">
                                    <label for="street_name" class="form-label">اسم الشارع</label>
                                    <input type="text" class="form-control" id="street_name" name="street_name">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6">
                                    <label for="district" class="form-label">الحي</label>
                                    <input type="text" class="form-control" id="district" name="district">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6">
                                    <label for="unit_governorate_id" class="form-label">المحافظة / المدينة</label>
                                    <select class="form-select" id="unit_governorate_id" name="unit_governorate_id">
                                        <option value="">اختر المحافظة</option>
                                        <?php foreach ($governorates as $gov): ?>
                                            <option value="<?= $gov['id'] ?>"><?= $gov['name'] ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>

                            <!-- Tax Registration Status -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="fas fa-file-invoice-dollar"></i> موقف التسجيل الضريبي
                                    </h6>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_registered" name="is_registered">
                                        <label class="form-check-label" for="is_registered">
                                            هل النشاط مسجل؟
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6" id="tax_registration_fields" style="display: none;">
                                    <label for="tax_registration_number" class="form-label">رقم التسجيل الضريبي (9 أرقام)</label>
                                    <input type="text" class="form-control" id="tax_registration_number" name="tax_registration_number" maxlength="9">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6" id="income_tax_office_field" style="display: none;">
                                    <label for="income_tax_office_id" class="form-label">مأمورية الدخل التابع لها</label>
                                    <select class="form-select" id="income_tax_office_id" name="income_tax_office_id">
                                        <option value="">اختر المأمورية</option>
                                        <?php foreach ($income_tax_offices as $office): ?>
                                            <option value="<?= $office['id'] ?>"><?= $office['name'] ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_vat_registered" name="is_vat_registered">
                                        <label class="form-check-label" for="is_vat_registered">
                                            هل النشاط مسجل في القيمة المضافة؟
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6" id="vat_tax_office_field" style="display: none;">
                                    <label for="vat_tax_office_id" class="form-label">مأمورية القيمة المضافة</label>
                                    <select class="form-select" id="vat_tax_office_id" name="vat_tax_office_id">
                                        <option value="">اختر المأمورية</option>
                                        <?php foreach ($vat_tax_offices as $office): ?>
                                            <option value="<?= $office['id'] ?>"><?= $office['name'] ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                            </div>

                            <!-- Electronic Invoice and Receipt -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="fas fa-receipt"></i> الفاتورة والإيصال الإلكتروني
                                    </h6>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">الفاتورة الإلكترونية</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="invoice_registered" name="invoice_registered">
                                                <label class="form-check-label" for="invoice_registered">مسجل فاتورة؟</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="invoice_required" name="invoice_required">
                                                <label class="form-check-label" for="invoice_required">ملزم فاتورة؟</label>
                                            </div>
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="invoice_issued" name="invoice_issued">
                                                <label class="form-check-label" for="invoice_issued">يصدر فاتورة؟</label>
                                            </div>
                                            <div class="mb-0">
                                                <label for="invoice_notes" class="form-label">ملاحظات الفاتورة</label>
                                                <textarea class="form-control" id="invoice_notes" name="invoice_notes" rows="3"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">الإيصال الإلكتروني</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="receipt_registered" name="receipt_registered">
                                                <label class="form-check-label" for="receipt_registered">مسجل إيصال؟</label>
                                            </div>
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="receipt_required" name="receipt_required">
                                                <label class="form-check-label" for="receipt_required">ملزم إيصال؟</label>
                                            </div>
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="receipt_issued" name="receipt_issued">
                                                <label class="form-check-label" for="receipt_issued">يصدر إيصال؟</label>
                                            </div>
                                            <div class="mb-0">
                                                <label for="receipt_notes" class="form-label">ملاحظات الإيصال</label>
                                                <textarea class="form-control" id="receipt_notes" name="receipt_notes" rows="3"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Additional Information -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="fas fa-sticky-note"></i> ملاحظات إضافية
                                    </h6>
                                </div>
                                <div class="col-md-6">
                                    <label for="daily_revenue" class="form-label">الإيرادات اليومية</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="daily_revenue" name="daily_revenue" step="0.01" min="0">
                                        <span class="input-group-text">جنيه</span>
                                    </div>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="has_other_activities" name="has_other_activities">
                                        <label class="form-check-label" for="has_other_activities">
                                            هل يوجد أنشطة أخرى غير مسجلة؟
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-12 mb-3" id="other_activities_field" style="display: none;">
                                    <label for="other_activities_notes" class="form-label">ملاحظات الأنشطة الأخرى</label>
                                    <textarea class="form-control" id="other_activities_notes" name="other_activities_notes" rows="3"></textarea>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="previous_visit" name="previous_visit">
                                        <label class="form-check-label" for="previous_visit">
                                            هل تم المرور على العين من قبل؟
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6" id="previous_visit_date_field" style="display: none;">
                                    <label for="previous_visit_date" class="form-label">تاريخ المرور السابق</label>
                                    <input type="date" class="form-control past-date" id="previous_visit_date" name="previous_visit_date">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6">
                                    <label for="property_type" class="form-label">نوعية العين</label>
                                    <select class="form-select" id="property_type" name="property_type">
                                        <option value="">اختر النوع</option>
                                        <option value="rent">إيجار</option>
                                        <option value="owned">تمليك</option>
                                    </select>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6">
                                    <label for="original_owner_name" class="form-label">اسم مالك العين الأصلي</label>
                                    <input type="text" class="form-control" id="original_owner_name" name="original_owner_name">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6">
                                    <label for="owner_national_id" class="form-label">الرقم القومي لمالك العين (14 رقم)</label>
                                    <input type="text" class="form-control" id="owner_national_id" name="owner_national_id" maxlength="14">
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-6">
                                    <label for="property_notes" class="form-label">ملاحظات حول العين وصاحب النشاط</label>
                                    <textarea class="form-control" id="property_notes" name="property_notes" rows="4"></textarea>
                                </div>
                            </div>

                            <!-- Attachments -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <h6 class="text-primary border-bottom pb-2 mb-3">
                                        <i class="fas fa-paperclip"></i> المرفقات
                                    </h6>
                                </div>
                                <div class="col-md-12">
                                    <label for="attachments" class="form-label">قائمة المرفقات</label>
                                    <textarea class="form-control" id="attachments" name="attachments" rows="3" placeholder="اكتب قائمة بالمرفقات المطلوبة أو المتوفرة"></textarea>
                                </div>
                            </div>

                            <!-- Submit Buttons -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex justify-content-between">
                                        <a href="../index.php" class="btn btn-secondary">
                                            <i class="fas fa-arrow-left"></i> العودة للرئيسية
                                        </a>
                                        <div>
                                            <button type="reset" class="btn btn-warning me-2">
                                                <i class="fas fa-undo"></i> إعادة تعيين
                                            </button>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save"></i> حفظ الحملة
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p>&copy; 2025 نظام تسجيل حملات الحصر الضريبي - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../assets/js/main.js"></script>

    <script>
        $(document).ready(function() {
            // Filter governorates based on selected sector
            $('#sector_id').change(function() {
                const sectorId = $(this).val();
                const governorateSelect = $('#governorate_id');

                governorateSelect.find('option').each(function() {
                    const option = $(this);
                    if (option.val() === '') {
                        option.show();
                        return;
                    }

                    if (sectorId === '' || option.data('sector') == sectorId) {
                        option.show();
                    } else {
                        option.hide();
                    }
                });

                governorateSelect.val('');
                updateReferenceNumber();
            });

            // Show/hide tax registration fields
            $('#is_registered').change(function() {
                if ($(this).is(':checked')) {
                    $('#tax_registration_fields, #income_tax_office_field').show();
                } else {
                    $('#tax_registration_fields, #income_tax_office_field').hide();
                }
            });

            // Show/hide VAT tax office field
            $('#is_vat_registered').change(function() {
                if ($(this).is(':checked')) {
                    $('#vat_tax_office_field').show();
                } else {
                    $('#vat_tax_office_field').hide();
                }
            });

            // Show/hide other activities field
            $('#has_other_activities').change(function() {
                if ($(this).is(':checked')) {
                    $('#other_activities_field').show();
                } else {
                    $('#other_activities_field').hide();
                }
            });

            // Show/hide previous visit date field
            $('#previous_visit').change(function() {
                if ($(this).is(':checked')) {
                    $('#previous_visit_date_field').show();
                } else {
                    $('#previous_visit_date_field').hide();
                }
            });

            // Update reference number when relevant fields change
            $('#campaign_date, #sector_id, #governorate_id, #campaign_number, #serial_number').change(updateReferenceNumber);

            function updateReferenceNumber() {
                const date = $('#campaign_date').val();
                const sectorId = $('#sector_id').val();
                const governorateId = $('#governorate_id').val();
                const campaignNumber = $('#campaign_number').val();
                const serialNumber = $('#serial_number').val();

                if (date && sectorId && governorateId && campaignNumber && serialNumber) {
                    const referenceNumber = generateReferenceNumber(date, sectorId, governorateId, campaignNumber, serialNumber);
                    $('#reference_number').val(referenceNumber);
                } else {
                    $('#reference_number').val('');
                }
            }
        });
    </script>
</body>
</html>