﻿Release Information
Microsoft Drivers 5.12 for PHP for SQL Server
Jan 2024


INTRODUCTION
------------
This file contains late-breaking or other important information
that supplements the Microsoft Drivers for PHP for SQL Server
documentation. You should read this file completely before 
installing the PHP driver.

Your feedback is very important to us and we will strive
to respond to your feedback in a timely manner. For information
about providing feedback by using the PHP driver blogs and online
forums, see the Microsoft Drivers for PHP for SQL Server page at
https://docs.microsoft.com/en-us/sql/connect/php/microsoft-php-driver-for-sql-server

INSTALLATION
------------
Instructions for installing the PHP driver are located in SQLSRV_Readme.htm.
Refer to that file for information about installing the PHP driver.

SUPPORTED OPERATING SYSTEMS
---------------------------
The Microsoft Drivers for PHP for SQL Server supports the following operating systems:
	o Windows Server 2022
	o Windows Server 2019
	o Windows Server 2016
	o Windows Server 2012
	o Windows Server 2012 R2
	o Windows 11
	o Windows 10
	o Windows 8.1

The list above is an example of some of the supported operating systems.

RELEASE CONTENTS
----------------
The Microsoft Drivers for PHP for SQL Server zip file unpacks the following files in the specified locations, relative to the selected directory:

<selected directory>\Microsoft DRIVERS FOR PHP FOR MICROSOFT SQL SERVER - Standalone (free) Use Terms.rtf
<selected directory>\release.txt
<selected directory>\SQLSRV_Readme.htm
<selected directory>\SQLSRV_ThirdPartyNotices.rtf
<selected directory>\php_pdo_sqlsrv_83_nts_x86.dll
<selected directory>\php_pdo_sqlsrv_83_nts_x64.dll
<selected directory>\php_pdo_sqlsrv_83_ts_x86.dll
<selected directory>\php_pdo_sqlsrv_83_ts_x64.dll
<selected directory>\php_sqlsrv_83_nts_x86.dll
<selected directory>\php_sqlsrv_83_nts_x64.dll
<selected directory>\php_sqlsrv_83_ts_x86.dll
<selected directory>\php_sqlsrv_83_ts_x64.dll
<selected directory>\php_pdo_sqlsrv_82_nts_x86.dll
<selected directory>\php_pdo_sqlsrv_82_nts_x64.dll
<selected directory>\php_pdo_sqlsrv_82_ts_x86.dll
<selected directory>\php_pdo_sqlsrv_82_ts_x64.dll
<selected directory>\php_sqlsrv_82_nts_x86.dll
<selected directory>\php_sqlsrv_82_nts_x64.dll
<selected directory>\php_sqlsrv_82_ts_x86.dll
<selected directory>\php_sqlsrv_82_ts_x64.dll
<selected directory>\php_pdo_sqlsrv_81_nts_x86.dll
<selected directory>\php_pdo_sqlsrv_81_nts_x64.dll
<selected directory>\php_pdo_sqlsrv_81_ts_x86.dll
<selected directory>\php_pdo_sqlsrv_81_ts_x64.dll
<selected directory>\php_sqlsrv_81_nts_x86.dll
<selected directory>\php_sqlsrv_81_nts_x64.dll
<selected directory>\php_sqlsrv_81_ts_x86.dll
<selected directory>\php_sqlsrv_81_ts_x64.dll

CHANGE LIST
-----------
Please visit the project on GitHub for the details of all the changes since last release 5.9:
https://github.com/microsoft/msphpsql/blob/master/CHANGELOG.md

KNOWN ISSUES
------------
Please visit the project on GitHub to view outstanding issues and report new ones: 
https://github.com/microsoft/msphpsql/issues

